<h3 id="DL7pU">Code 验证码输入框</h3>
<h4 id="H23rK">组件名: xt-code</h4>
<h3 id="aVAyi">安装方式</h3>
<h4 id="trm7a">本组件符合 easycom 规范，HBuilderX 3.1.0 起，只需将本组件导入项目，在页面 template 中即可直接使用，无需在页面中 import 和注册 components</h4>
<h3 id="mb3ZK">基本用法</h3>
```vue
<template>
	<view class="xt">
		<xt-code v-model='code' @complete="complete" @confirm="confirm"></xt-code>
		<xt-code type="bottom" @complete="complete" @confirm="confirm"></xt-code>
		<xt-code type="middle" @complete="complete" @confirm="confirm"></xt-code>
	</view>
</template>
<script>
	export default {
		data() {
			return {
				code: '123'
			}
		},
		methods: {
			complete(e) {
				console.log(e);
			},
			confirm(e) {
				console.log(e);
			}
		}
	}
</script>
<style scoped>
	.xt {
		padding: 50rpx;
		display: flex;
		flex-direction: column;
		gap: 50rpx;
	}
</style>
```

<h3 id="KGPWw">API</h3>
| 属性				| 类型		| 默认值		| 描述								|
| ---				| ---		| ---		| ---								|
| `value`			| `String`	| -			| 绑定的值							|
| `fontSize`		| `String`	| `30rpx`	| 字体大小							|
| `size`			| `String	|Number`	| `60rpx`							| 输入框大小	|
| `color`			| `String`	| `#303133`	| 字体颜色							|
| `cursorColor`		| `String`	| `#1e90ff`	| 光标颜色							|
| `inactiveColor`	| `String`	| `#a4b0be`	| 输入框未激活时的颜色					|
| `activeColor`		| `String`	| `#409EFF`	| 输入框激活时的颜色					|
| `type`			| `String`	| `box`		| 输入框类型，可选 `middle`，`bottom`	|
| `count`			| `Number`	| `4`		| 验证码位数							|
| `stick`			| `Boolean`	| `false`	| 是否开启粘贴						|


<h3 id="CVwWk">Event</h3>
| 事件名 | 类型 | 说明 |
| --- | --- | --- |
| `complete` | `Function` | 位数达标事件 |
| `confirm` | `Function` | 完成事件 |
| `paste` | `Function` | 粘贴事件 |


