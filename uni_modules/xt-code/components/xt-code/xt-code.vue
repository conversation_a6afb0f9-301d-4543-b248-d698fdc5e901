<template>
	<view class="xt-code-main">
		<view class="xt-code" @touchstart="onTouchStart" @touchend="onTouchEnd">
			<view class="xt-code-paste" v-if="stick && isPaste" @click="paste">
				<view class="xt-code-paste-text">粘贴</view>
			</view>
			<view class="xt-code-input">
				<input type="text" :value="inputValue" @input="codeInput" :maxlength="count" :cursor='codeCursor'
					:focus="codeFocus" @blur="codeBlur" confirm-type="done" @confirm="codeConfirm" />
			</view>
			<view class="xt-code-item" :class="[type=='box'?type:type == 'bottom'?type:'']" v-for="(item,i) in count"
				:key="i" @click="onClick(i)" :style="{
				'font-size': fontSize,
				width: handlePixel(size),
				height: handlePixel(size),
				borderColor: index == i ?activeColor:inactiveColor,
			}">
				<view>
					{{ code[i] || ''}}
				</view>
				<view class="xt-code-middle" v-if="type=='middle' && (!code[i] && code[i] != '0')" :style="{
					backgroundColor: index == i ?activeColor:inactiveColor,
				}">

				</view>
				<view class="xt-code-cursor"
					v-if="(type == 'middle' && (code[i] == '0' || code[i]) && index == i) || (type != 'middle'&& index == i)"
					:style="{
					backgroundColor: cursorColor,
				}"></view>
			</view>
		</view>
	</view>
</template>
<script>
	import props from "./props.js";
	/**
	 * Code 验证码输入框
	 * @description 本组件提供一个验证码输入框的功能，让开发者开发起来更加得心应手。减少重复的模板代码
	 * @tutorial https://ext.dcloud.net.cn/plugin?name=xt-code
	 *
	 * @property {String}          value             	绑定的值
	 * @property {String}          fontSize             字体大小(默认30rpx)
	 * @property {String|Number}   size             	输入框大小(默认60rpx)
	 * @property {String}          color             	字体颜色
	 * @property {String}          cursorColor          光标颜色
	 * @property {String}          inactiveColor        输入框未激活颜色
	 * @property {String}          activeColor        	输入框激活颜色
	 * @property {String}          type             	输入框类型,可选middle,bottom(默认box)
	 * @property {Number}          count             	验证码位数(默认4)
	 * @property {Boolean}         stick             	是否开启粘贴(默认false)
	 * @event {Function}           complete            	位数达标事件
	 * @event {Function}           confirm            	完成事件
	 * @event {Function}           paste            	粘贴事件
	 * @example <xt-code></xt-code>
	 */
	export default {
		name: "xt-code",
		mixins: [props],
		// #ifdef MP-WEIXIN
		options: {
			virtualHost: true,
		},
		// #endif
		data() {
			return {
				inputValue: '',
				// 验证码数据
				code: [],
				// 聚焦索引
				index: 0,
				// 是否聚焦
				codeFocus: true,
				// 光标位置
				codeCursor: 0,
				// 定时器
				timer: null,
				// 是否显示粘贴
				isPaste: false,
			};
		},
		watch: {
			// #ifdef VUE3
			modelValue: {
				immediate: true,
				handler(newValue, oldValue) {
					if (this.code.length > 0) return
					this.code = newValue.split('');
					this.formValidate(this, "change")
				}
			},
			// #endif
			value: {
				immediate: true,
				handler(newValue, oldValue) {
					if (this.code.length > 0) return
					this.code = newValue.split('');
					this.formValidate(this, "change")
				}
			},
		},
		methods: {
			handlePixel(value = 'auto', unit = 'px', count, type) {
				const regular = /^[\+-]?(\d+\.?\d*|\.\d+|\d\.\d+e\+\d+)$/;
				const unitRegex = /^(\d+)([a-zA-Z%]+)$/;
				let valueNum = value;
				let valueUnit = unit;
				let countNum = count;
				let countUnit = unit;
				let result = value;
				if (typeof value === 'string' && unitRegex.test(value)) {
					const valueMatch = value.match(unitRegex);
					valueNum = Number(valueMatch[1]); // 数字部分
					result = Number(valueMatch[1]);
					valueUnit = valueMatch[2]; // 单位部分
				}
				if (typeof count === 'string' && unitRegex.test(count)) {
					const countMatch = count.match(unitRegex);
					countNum = Number(countMatch[1]); // 数字部分
					countUnit = countMatch[2]; // 单位部分
				}
				if (valueNum > 0 && countNum > 0) {
					switch (type) {
						case '+':
							result = `${valueNum + countNum}`;
							break;
						case '-':
							result = `${valueNum - countNum}`;
							break;
						case '*':
							result = `${valueNum * countNum}`;
							break;
						case '/':
							result = `${valueNum / countNum}`;
							break;
					}
				}
				return result + valueUnit;
			},
			getParent(name = undefined) {
				let parent = this.$parent
				while (parent) {
					// 父组件
					if (parent.$options && parent.$options.name !== name) {
						// 如果组件的name不相等，继续上一级寻找
						parent = parent.$parent
					} else {
						return parent
					}
				}
				return false
			},
			formValidate(instance, event) {
				const formItem = this.getParent.call(instance, 'xt-form-item')
				const form = this.getParent.call(instance, 'xt-form')
				if (formItem && form) {
					form.validateField(formItem.prop, () => {}, event)
				}
			},
			// 触摸开始时的事件
			onTouchStart() {
				this.timer = setInterval(() => {
					this.isPaste = true
					clearInterval(this.timer)
				}, 1000)
			},
			onTouchEnd() {
				clearInterval(this.timer)
			},
			// 粘贴事件
			paste() {
				const that = this;
				this.$emit('paste')
				uni.getClipboardData({
					success: function(res) {
						if (res.data) {
							const value = res.data.slice(0, that.count)
							that.code = value.split('');
							that.index = that.code.length + 1;
							// #ifdef VUE2
							that.$emit('input', value);
							// #endif
							// #ifdef VUE3
							that.$emit('update:modelValue', value);
							// #endif
							if (that.code.length >= that.count) {
								that.$emit('complete', value)
							}
						} else {
							console.error("剪贴板为空");
						}
					},
					fail: function(err) {
						console.error(err);
					},
					complete: function() {
						that.isPaste = false;
					}
				});
			},
			codeInput(e) {
				if (this.isPaste) {
					this.isPaste = false
				}
				const value = e.detail.value;
				const len = value.length;
				const valueList = value.split('');
				const index = this.findFirstChange(this.code, valueList).index;
				// 增加
				if (len > this.code.length) {
					this.index = index;
				} else {
					index == 0 ? this.index = 0 : this.index = index - 1;
				}
				this.code = valueList;
				// #ifdef VUE2
				this.$emit('input', value);
				// #endif
				// #ifdef VUE3
				this.$emit('update:modelValue', value);
				// #endif
				this.codeCursor = this.index + 1;
				if (this.code.length == this.count) {
					this.$emit('complete', this.code.join(''))
				}
				this.formValidate(this, "change")
			},
			// 找出变化值
			findFirstChange(arr1, arr2) {
				// 确定比较的最大长度
				let maxLength = Math.max(arr1.length, arr2.length);
				// 遍历数组，找到第一个变化的值
				for (let i = 0; i < maxLength; i++) {
					if (arr1[i] !== arr2[i]) {
						return {
							// 变化的列数，从 0 开始
							index: i,
							// 变化前的值
							oldValue: arr1[i],
							// 变化后的值
							newValue: arr2[i]
						};
					}
				}
				return null;
			},
			codeBlur() {
				this.codeFocus = false;
				this.formValidate(this, "blur");
			},
			codeConfirm() {
				this.$emit('confirm', this.code.join(''))
			},
			onClick(e) {
				if (this.code.join('') !== this.inputValue) {
					this.inputValue = this.code.join('');
				}
				if (this.code[e]) {
					this.index = e;
				} else {
					this.code.length == 0 ? this.index = 0 : this.index = this.code.length - 1;
				}
				this.codeCursor = this.index + 1;
				this.codeFocus = true;
			}
		},
	};
</script>
<style scoped lang="scss">
	@import "./style.scss";
</style>