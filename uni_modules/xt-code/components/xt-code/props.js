export default {
	props: {
		// 绑定的值
		modelValue: {
			type: [String, Number],
			default: () => ''
		},
		// 绑定的值
		value: {
			type: [String, Number],
			default: () => ''
		},
		// 输入框字体大小
		fontSize: {
			type: String,
			default: '30rpx'
		},
		// 输入框字体颜色
		color: {
			type: String,
			default: '#ffffff',
		},
		type: {
			type: String,
			default: 'box'
		},
		count: {
			type: Number,
			default: 7
		},
		size: {
			type: [String, Number],
			default: '60rpx'
		},
		cursorColor: {
			type: String,
			default: '#1e90ff'
		},
		stick: {
			type: Boolean,
			default: false
		},
		inactiveColor: {
			type: String,
			default: '#a4b0be'
		},
		activeColor: {
			type: String,
			default: '#409EFF'
		},
	}
}