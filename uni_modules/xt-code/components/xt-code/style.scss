.xt-code-main {
	position: relative;
}
.xt-code{
	position: relative;
	display: flex;
	flex-direction: row;
	gap: 15rpx;
	align-items: center;
	justify-content: center;
}
.xt-code-input{
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	z-index: -1;
	opacity: 0;
}
.xt-code-item{
	position: relative;
	display: flex;
	align-items: center;
	justify-content: center;
	width: 100%;
	height: 100%;
	cursor: text;
	&.box{
		border: 1px solid #ccc;
		border-radius: 15rpx;
	}
	&.bottom{
		border-bottom: 5rpx solid #ccc;
	}
}
.xt-code-cursor{
	height: 80%;
	width: 5rpx;
	border-radius: 88px;
	animation: scale 0.6s infinite;
}
.xt-code-middle{
	position: absolute;
	width: 80%;
	height: 10rpx;
	border-radius: 40%;
}
@keyframes scale {
    0%, 100% {
       height: 60%;
    }
    50% {
	height: 80%;
    }
}
.xt-code-paste{
	position: absolute;
	top: -8rpx;
	transform: translateY(-100%);
	background-color: rgba(0, 0, 0, 0.5);
	padding: 0 5rpx;
	border-radius: 8rpx;
	text-align: center;
}
.xt-code-paste-text{
	position: relative;
	font-size: 20rpx;
	color: #fff;
	&::after{
		content: '';
			position: absolute;
			bottom: -10rpx;
			left: 50%;
			transform: translateX(-50%);
			width: 0;
			height: 0;
			border-left: 4rpx solid transparent;
			border-right: 4rpx solid transparent;
			border-top: 8rpx solid rgba(0, 0, 0, 0.5);		
	}
}