<template>
	<view>
		<view class="turntable_wp sol-class">
			<image src="/uni_modules/q-turntable/static/s-dial_bg.png"
				:style="'-webkit-transform:rotate(' + deg + 'deg) translateZ(0);transform:rotate(' + deg + 'deg) translateZ(0)'">
			</image>
			<view class="turntable_pointer" @tap="start">
				<image src="/uni_modules/q-turntable/static/s-dial_pointer.png"></image>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			deg: -60,// 旋转的角度
			singleAngle: '',// 每片扇形的角度
			isStart: false,
		};
	},

	components: {},
	props: {
		// 划分区域
		areaNumber: {
			type: Number,
			default: 6
		},
		// 速度
		speed: {
			type: Number,
			default: 20
		},
		// 中奖概率
		probabilities: {
			type: Array,
			default: () => [0.2, 0.2, 0.2, 0.2, 0.1, 0.1] // 每个奖项的概率
		},
	},

	beforeMount() {
		this.init();
	},

	methods: {
		init() {
			let { areaNumber } = this;
			const singleAngle = 360 / areaNumber;
			this.singleAngle = singleAngle;
		},

		// 点击开始抽奖
		start() {
			this.$emit('start');
		},

		// begin(awardNumer) {
		// 	var deg = this.deg;
		// 	var singleAngle = this.singleAngle;
		// 	var speed = this.speed;
		// 	var isStart = this.isStart;
		// 	if (isStart) return;
		// 	this.isStart = true;
		// 	let endAddAngle = 0;
		// 	endAddAngle = 360 - ((awardNumer - 1) * singleAngle + singleAngle / 1); // 中奖角度


		// 	const rangeAngle = (Math.floor(Math.random() * 7) + 4) * 360; // 随机旋转几圈再停止

		// 	console.log(endAddAngle);
		// 	let cAngle;
		// 	deg = 0;
		// 	this.timer = setInterval(() => {
		// 		if (deg < rangeAngle) {
		// 			deg += speed;
		// 		} else {
		// 			cAngle = (endAddAngle + rangeAngle - deg) / speed;
		// 			cAngle = cAngle > speed ? speed : cAngle < 1 ? 1 : cAngle;
		// 			deg += cAngle;

		// 			if (deg >= endAddAngle + rangeAngle) {
		// 				deg = endAddAngle + rangeAngle;
		// 				this.isStart = false;
		// 				clearInterval(this.timer);
		// 				this.$emit('success');
		// 			}
		// 		}

		// 		this.deg = deg;
		// 	}, 1000 / 60);
		// },

		// begin() {
		// 	var deg = this.deg;
		// 	var singleAngle = this.singleAngle;
		// 	var speed = this.speed;
		// 	var isStart = this.isStart;
		// 	if (isStart) return;
		// 	this.isStart = true;

		// 	// 根据概率生成中奖编号
		// 	let awardNumer = this.getAwardNumber();

		// 	let endAddAngle = 360 - ((awardNumer - 1) * singleAngle + singleAngle / 1); // 中奖角度
		// 	// let endAddAngle = 360 - ((awardNumer - 1) * singleAngle + singleAngle / 2); // 中奖角度
		// 	const rangeAngle = (Math.floor(Math.random() * 7) + 4) * 360; // 随机旋转几圈再停止

		// 	let cAngle;
		// 	deg = 0;
		// 	this.timer = setInterval(() => {
		// 		if (deg < rangeAngle) {
		// 			deg += speed;
		// 		} else {
		// 			cAngle = (endAddAngle + rangeAngle - deg) / speed;
		// 			cAngle = cAngle > speed ? speed : cAngle < 1 ? 1 : cAngle;
		// 			deg += cAngle;

		// 			if (deg >= endAddAngle + rangeAngle) {
		// 				deg = endAddAngle + rangeAngle;
		// 				this.isStart = false;
		// 				clearInterval(this.timer);
		// 				this.$emit('success', awardNumer); // 传递中奖编号
		// 			}
		// 		}

		// 		this.deg = deg;
		// 	}, 1000 / 60);
		// },

		begin(awardNumer) {
			var deg = this.deg;
			var singleAngle = this.singleAngle;
			var speed = this.speed;
			var isStart = this.isStart;
			if (isStart) return;
			this.isStart = true;

			// 使用外部传入的中奖编号
			// let endAddAngle = 360 - ((awardNumer - 1) * singleAngle + singleAngle / 1); // 中奖角度
			let endAddAngle = 360 - ((awardNumer + 2) * singleAngle + singleAngle / 1); // 中奖角度
			const rangeAngle = (Math.floor(Math.random() * 7) + 4) * 360; // 随机旋转几圈再停止

			let cAngle;
			deg = 0;
			this.timer = setInterval(() => {
				if (deg < rangeAngle) {
					deg += speed;
				} else {
					cAngle = (endAddAngle + rangeAngle - deg) / speed;
					cAngle = cAngle > speed ? speed : cAngle < 1 ? 1 : cAngle;
					deg += cAngle;

					if (deg >= endAddAngle + rangeAngle) {
						deg = endAddAngle + rangeAngle;
						this.isStart = false;
						clearInterval(this.timer);
						this.$emit('success', awardNumer); // 传递中奖编号
					}
				}
				console.log('deg', deg);
				this.deg = deg;
			}, 1000 / 60);
		},

		// 根据概率生成中奖编号
		getAwardNumber() {
			const probabilities = this.probabilities;
			const random = Math.random();
			let cumulative = 0;

			for (let i = 0; i < probabilities.length; i++) {
				cumulative += probabilities[i];
				if (random < cumulative) {
					return i + 1; // 奖项编号从 1 开始
				}
			}

			return probabilities.length; // 默认返回最后一个奖项
		},
	},
};
</script>
<style>
.turntable_wp {
	width: 446rpx;
	height: 446rpx;
	position: relative;
	margin: 0 auto;
}

.turntable_wp image {
	display: block;
	width: 100%;
	height: 100%;
}

.turntable_wp .turntable_pointer {
	position: absolute;
	width: 108rpx;
	height: 172rpx;
	top: 50%;
	left: 50%;
	margin: -120rpx 0 0 -54rpx;
}
</style>
