## 1.9.7（2023-08-02）
本次更新：
- 调整 `draw-before` 为自定义函数，且该函数为必备函数，转盘能否启动，将根据该函数中调用 `callback` 时传递的 `Boolean` 进行判断
## 1.9.6.2（2023-07-28）
本次更新：
- 更新示例项目
## 1.9.6.1（2023-07-28）
本次更新：
- 修复参数文档排版问题
## 1.9.6（2023-07-28）
本次更新：
- 新增`drawStartBefore`钩子，请看说明文档
## 1.9.5（2023-07-12）
本次更新：
- 优化 `duration` 参数的变更需要刷新才能生效的开发体验
- 新增 `duration` 和 `ringCount` 设置不合理时的 `console` 提醒
## 1.9.4（2023-07-06）
本次更新：
- 修复某些情况文字无法换行的问题
- 参数`strMaxLen`设置为`0`时不限制文字长度
## 1.9.3（2023-06-12）
本次更新：
- 新增 `selfTime` 参数，查看文档说明
- 示例项目分离 `uni-popup` 用例为独立页面进行展示
## 1.9.2（2023-05-22）
本次更新：
- 新增 `renderDelay` 参数，请查看文档
- 示例项目新增 `ui-popup` 组件的用例，使用 `uni-popup` 包裹转盘时，请前往 `uni-popup` 组件文档关注平台兼容性问题
## 1.9.1（2023-03-07）
本次更新：
- 新增 `selfRotaty` 自转参数，视觉效果上存在细微瑕疵，欢迎PR
## 1.8.33（2022-07-04）
本次更新：
- 统一奖品图片下载方式
## 1.8.32（2022-06-20）
本次更新：
- 调整平台兼容性（因 HBX 存在 bug，导致平台兼容性的更新多次失败，近几次的更新都可以忽略）
## 1.8.31（2022-06-20）
本次更新：
- 无意义的更新，请忽略
## 1.8.30（2022-06-20）
本次更新：
- 调整平台兼容性
## 1.8.29（2022-06-19）
本次更新：
- 调整平台兼容信息
## 1.8.28（2022-06-19）
本次更新：
- 修复单个尺寸过大的 canvas 在 H5/APP-vue iOS/Safari 中存在可能无法绘制成功的问题
## 1.8.27（2022-06-01）
本次更新：
- 优化示例项目
## 1.8.26（2022-06-01）
本次更新：
- 修复奖品图片裁切为圆形时在安卓机器不显示的问题
## 1.8.25（2022-05-31）
本次更新：
- 修复部分安卓下载图片得到.unknown格式文件的问题
## 1.8.24（2022-05-09）
本地缓存：
- 优化示例项目
## 1.8.23（2022-05-09）
本地缓存：
- 优化清除文件缓存的方法
## 1.8.22（2022-05-09）
本次更新：
- 调整计算转盘绘制的方式
## 1.8.21（2022-05-08）
本次更新：
- 调整示例项目中本地图片的引入方式
## 1.8.20（2022-04-29）
本次更新：
- 修复转盘在某个临界点可以出现多次触发的问题
## 1.8.19（2022-04-27）
本次更新：
- 奖品文字的绘制由先前的两行变成多行，根据设定的每行文字的长度分段绘制
## 1.8.18（2022-04-25）
本次更新：
- 减少小程序平台的 delay
## 1.8.17（2022-03-23）
本次更新：
- 新增配置项 `imgCircled` 奖品图片是否裁切为圆形，默认不裁切
## 1.8.16（2022-03-04）
本次更新：
- 示例项目新增绘制时长的计算，方便开发时定位绘制慢的问题
## 1.8.15（2022-03-02）
本次更新：
- 优化一处错误提示信息的展现方式
## 1.8.14（2021-11-29）
本次更新：
- 示例项目中新增开放自定义权重最大值，没有自定义则取权重数组中的最大值
- 更新文档
## 1.8.13（2021-11-03）
本次更新：
- 注释 `1.8.12` 版本中调试时的代码
## 1.8.12（2021-11-03）
本次更新：
- 修复一些老机型不支持 `flex` 导致布局错乱的问题
## 1.8.11（2021-10-29）
本次更新：
- 优化示例项目中模拟接口访问的速度
## 1.8.10（2021-10-19）
本次更新：
- 优化组件代码
- 更新示例项目
## 1.8.9（2021-09-28）
本次更新：
- 移除内置的 `奖品准备中...` 提示
## 1.8.8（2021-09-27）
本次更新：
- 修复 `1.8.6` 引起的非微信小程序平台绘制异常的问题
## 1.8.7（2021-09-23）
本次更新：
- 优化项目中使用到的图片大小
## 1.8.6（2021-09-23）
本次更新：
- 修复小程序平台在绘制 `base64` 格式的图片时无法在真机模式下正常显示的问题
## 1.8.5（2021-09-13）
本次更新：
- 修复一个已知问题
## 1.8.4（2021-09-12）
本次更新：
- 调整 `strFontColor` 为 `strFontColors`，现在可以设置每个区块的文字颜色，详见文档说明
## 1.8.3（2021-09-12）
本次更新：
- 修复因 `1.8.0` 改动引起的文字方向、无奖品图时绘制异常的问题
- 新增 `imgDrawed` 是否绘制奖品图片的配置项 ，默认为 `true`
## 1.8.2（2021-09-10）
本次更新：
**不兼容旧版本的更新**
- 移除配置项 `strKey` 字段
- 调整 `prizeList` 结构
## 1.8.1（2021-09-06）
本次更新：
- 修复 `hbx3.1.22` 在小程序平台处理 `id-name` 存在解析错误的问题
## 1.8.0（2021-09-06）
本次更新：
**该版本更新涉及破坏性的变更，请重新查看 `API - Props` 的部分**
- `px` 全面调整为 `rpx` 单位，多个`Props` 的参数相应调整，请查看文档
 - 新增 `pixelRatio` 参数，该参数为设计稿的设备像素比基准值，默认为 `2` 倍素
## 1.7.18（2021-09-05）
本次更新：
- 修复一个已知问题
## 1.7.17（2021-08-23）
本次更新：
- 更新示例项目
## 1.7.16（2021-08-14）
本次更新：
- 更新示例项目
## 1.7.15（2021-08-03）
本次更新：
- 新增文字竖向展示的功能，详见文档说明
## 1.7.13（2021-08-02）
本次更新：
- 更新文档
## 1.7.12（2021-07-30）
本次更新：
- 修复示例项目的已知问题
- 现已提供Almost-Lottery抽奖转盘的uniCloud云端一体页面模板
- 现已提供Almost-Lottery抽奖转盘云端一体页面配套的Admin配置中心
## 1.7.11（2021-07-22）
本次更新：
- 修复部分安卓手机文字大小异常的问题
- 字段 `strHeightMultiple` 更换为 `strLineHeight`
## 1.7.10（2021-07-12）
本次更新：
- 修复奖品名称 `name` 为空字符串时无法成功绘制转盘的问题
- 新增 `prizeNameDrawed` 是否绘制奖品名称的配置项，现在可以仅展示奖品图片了
## 1.7.9（2021-07-09）
本次更新：
- 优化组件内部代码
- 修复奖品图片已然是 `base64` 格式时导致转盘绘制失败的问题
- 文档新增QQ群号，让沟通更便捷
## 1.7.8（2021-07-08）
本次更新：
- 调整 `Canvas` 默认宽高为 `280`
## 1.7.7（2021-07-08）
本次更新：
- 新增多个配置项，满足更多自定义需求
- 优化多行文本情况下非中文字符的字节处理
- 修复偶发的第一条数据文本不居中显示的问题
## 1.7.6（2021-07-02）
本次更新：
- 调整 `imageWidth` 、 `imageHeight` 字段为 `imgWidth` 、 `imgHeight` 
- 更新示例项目
## 1.7.5（2021-07-01）
本次更新：
- 新增配置项 `imgMarginStr` 奖品图片距离奖品文字的距离
## 1.7.4（2021-06-28）
本次更新：
- 新增轮盘旋转或指针旋转配置项
- 转盘内置的外环图片以及按钮图片统一调整为 `image` 展示
- 更新相关文档说明
## 1.7.3（2021-06-16）
本次更新：
- 优化错误提示
- 优化示例项目
- 优化文档说明
## 1.7.2（2021-06-11）
本次更新：
- 新增 `canvasId` 参数配置项，多画板情况下需要配置不同的 `canvasId`
- 优化多画板情况下的缓存功能
- 优化示例项目
- 修改文档说明
## 1.7.1（2021-06-10）
本次更新：
- 优化示例项目中的注释
## 1.7.0（2021-06-04）
本次更新：
- 修复 `1.6.1` 引起的多行奖品文字行高异常的问题
- 新增配置转盘外环和抽奖按钮图片的功能，详见文档说明
- 更新示例项目，新增抽奖次数等业务有关的逻辑供参考
## 1.6.1（2021-05-28）
本次更新：
- 修复小程序平台画板模糊的问题
## 1.6.0（2021-05-28）
本次更新：
- 新增奖品区块是否开启描边的配置项，默认不开启
- 调整画板缓存为默认不开启
- 优化代码
- 优化文档说明
- 更新示例项目并修改部分注释
## 1.5.13（2021-05-22）
本次更新：
- 优化文档说明
- 更新示例项目
## 1.5.12（2021-05-22）
本次更新：
- 新增配置项 `strokeColor` 奖品区块边框颜色
- 更新文档说明
## 1.5.11（2021-05-19）
本次更新：
- 新增`strMarginOutside`参数，用于设置奖品文字距离边缘的距离
- 修复奖品文字在某些情况下不是居中显示的问题
## 1.5.10（2021-05-19）
本次更新：
- 修复示例项目中权重值相同时的取值逻辑
## 1.5.9（2021-05-14）
本次更新：
- 调整代码，优化小程序端的展示
## 1.5.8（2021-05-12）
本次更新：
- 文档增加预警提示：不再维护非 `uni_modules` 模式下的版本
## 1.5.7（2021-05-12）
本次更新：
- 修复小程序平台奖品名称不清晰的问题
## 1.5.6（2021-03-18）
本次更新：
- 适配 uni_modules 插件模式
