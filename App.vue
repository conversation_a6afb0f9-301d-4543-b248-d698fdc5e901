<script>
	export default {
		onLaunch: function() {
			console.log('App Launch')
			//   uni.hideTabBar();
		},
		onShow: function() {
			console.log('App Show')
		},
		onHide: function() {
			console.log('App Hide')
		}
	}
</script>

<style lang="scss">
	@import '@/static/styles/common.scss';

	/*每个页面公共css */
	page {
		background-color: #000C1F;
		min-height: 94vh;
		// height: 100vh;
		// overflow: hidden;
	}

	// 全局页面切换动画
	.uni-page-head,
	.uni-page-body {
		background: transparent;
	}

	.uni-page {
		@extend .page-transition;
		background: transparent;
		// height: 100vh;
		min-height: 100vh;
		// overflow: hidden;
	}

	// 禁用页面滚动
	::-webkit-scrollbar {
		display: none;
		width: 0;
		height: 0;
		color: transparent;
	}
</style>
