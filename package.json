{"name": "golden", "version": "1.0.0", "description": "", "repository": {"type": "git", "url": "\\"}, "dependencies": {"@solana/web3.js": "^1.98.0", "buffer": "^6.0.3", "ethers": "^6.13.2", "html2canvas": "^1.4.1", "lodash": "^4.17.21", "vant": "^4.9.19", "vconsole": "^3.15.1", "vue-i18n": "^11.0.0-rc.1", "web3": "^4.11.0"}, "license": "ISC", "author": "", "type": "commonjs", "main": "main.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "devDependencies": {"@dcloudio/types": "^3.4.15", "@types/uni-app": "^1.4.8"}}