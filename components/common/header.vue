<template>
  <!-- 顶部导航栏 -->
  <view class="header">
    <view class="logo">
      <image src="/static/images/logo.png" mode="aspectFit"></image>
      <!-- <text>{{ useraddress || "连接钱包" }}</text> -->
    </view>
    <view style="display: flex;">
          <view class="account">
          <!-- <text style="display: none;">{{ useraddress }}</text> -->
          <text >{{ useraddress || "连接钱包" }}</text>
        
          <!-- 语言图标 -->
          <image src='/static/images/globe.png' style="display: none;" mode="aspectFit" class="lang-icon" @tap="toggleLangList"></image>
          <!-- <image src='/static/images/globe.png' mode="aspectFit" class="lang-icon" @tap="toggleLangList"></image> -->
        </view>
     <!-- 资产 -->
       <view class="asset_balance" style="display: flex;flex-direction: column;">
          <image style="width: 48rpx;height: 48rpx;" src='/static/images/zichan.png' mode="aspectFit" class="lang-icon" ></image>
          <text  class="asset_text">资产</text>
       </view>
    </view>
    
    <!-- 语言选择列表 -->
    <view class="lang-list"  v-if="showLangList">
      <view class="lang-mask" @tap="toggleLangList"></view>
      <view class="lang-content">
        <view class="lang-title">{{ $t('header.lang-title') }}</view>
        <view class="lang-item" v-for="(item, index) in langList" :key="index" :class="{ active: item.active }"
          :lang="item.lang" @tap="selectLang(index)">
          <view class="left">
            <image :src="item.flag" mode="aspectFit" class="flag"></image>
            <text class="name">{{ item.name }}</text>
          </view>
          <view class="star" :class="{ active: item.active }"></view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { getBlockchainConfig } from "../../api/ailk";
export default {
  props: ['dengdai'], // 添加 props 声明
  data() {
    return {
      address: '',
      useraddress: '',
      active: 'tc',
      config: '',
      showLangList: false,
      langList: [
        { name: 'English', lang: 'us', flag: '/static/images/flags/us.png', active: false },
        { name: 'Chinese', lang: 'cn', flag: '/static/images/flags/cn.svg', active: true },
        { name: 'Russian', lang: 'ru', flag: '/static/images/flags/ru.png', active: true },
        { name: 'German', lang: 'de', flag: '/static/images/flags/de.png', active: true },
        { name: 'French', lang: 'fr', flag: '/static/images/flags/fr.png', active: true },
        { name: 'Arabic', lang: 'ar', flag: '/static/images/flags/ar.jpg', active: true },
        { name: 'Japanese', lang: 'ja', flag: '/static/images/flags/ja.png', active: true },
        { name: 'Korean', lang: 'ko', flag: '/static/images/flags/ko.webp', active: true },
        { name: 'Vietnamese', lang: 'vi', flag: '/static/images/flags/vi.gif', active: true },
        { name: 'Thai', lang: 'th', flag: '/static/images/flags/th.png', active: true },
      ],
      configuser: [],
    }
  },
  inject: ['reload'],
  methods: {
    toggleLangList() {
      this.showLangList = !this.showLangList
    },
    async selectLang(index) {
      this.langList.forEach(item => {
        item.active = false
      })
      this.langList[index].active = true

      sessionStorage.setItem("lang", this.langList[index].lang)
      sessionStorage.setItem("selectedLangIndex", index.toString())

      this.showLangList = false

      uni.showToast({
        title: this.$t('header.switch-success', { lang: this.langList[index].name }),
        icon: 'none'
      })
      // 切换语言
      this.$i18n.locale = this.langList[index].lang;
      // 通知父组件语言切换
      this.$emit('langSwitched', this.langList[index].lang);
      await this.reload(); // 重新加载数据
      // location.reload(); // 刷新页面
    },
  },
  // 组件加载时

  async mounted() { // 添加 async 声明
    if (typeof this.dengdai === 'function') {
      await this.dengdai();
    }
    this.address = sessionStorage.getItem("address");
    console.log("address", this.address);
    if (this.address) {
      this.useraddress = this.address.slice(0, 4) + "****" + this.address.slice(-4);
    } else {
      this.useraddress = "未连接"; // 或者提供一个默认值
    }
    // 初始化时设置当前选中的语言
    const currentLang = sessionStorage.getItem("lang") || 'cn';
    this.langList.forEach(item => {
      item.active = item.lang === currentLang;
    });
    //  this.getBlockchainConfig()
  },
  //  async getBlockchainConfig() {
  //           const res = await getBlockchainConfig()
  //           console.log(res);
  //           this.config = res.data
  //       }
}
</script>

<style lang="scss" scoped>
.header {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12rpx 24rpx;
  background-color: #1a1a1a;
  z-index: 999;

  .logo {
    display: flex;
    align-items: center;
    image {
      width: 48rpx;
      height: 48rpx;
    }
    text {
      padding: 8rpx 8rpx;
      border: 1rpx solid #ffffff;
      border-radius: 10rpx;
      margin-left: 8rpx;
      line-height: 33rpx;
      font-size: 28rpx;
      color: #ffffff;
    }
  }
  .account {
    display: flex;
    align-items: center;
    background: rgba(255, 255, 255, 0.1);
    padding: 11rpx 16rpx;
    // border-radius: 30rpx;
    border-radius: 10px 10px 10px 10px;
    border: 1px solid #353535;
    margin-right: 10rpx;
    text {
      font-size: 28rpx;
      color: #ffffff;
    }
    image.lang-icon {
      width: 36rpx;
      height: 36rpx;
      // margin-left: 12rpx;
    }
   
  }
   .asset_balance{
      display: flex;
      flex-direction: column;
      text-align: center;
      image{
         width: 48rpx;
         height: 48rpx;
      }
      .asset_text{
        font-weight: 400;
        font-size: 10px;
        color: #ffffff;
      }
  }
  
}

.container {
  min-height: 100vh;
  background-color: #1A1A1A;
  //   padding: 24rpx;
}

.lang-list {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;

  .lang-mask {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
  }

  .lang-content {
    position: absolute;
    top: 100rpx;
    right: 24rpx;
    width: 400rpx;
    background-color: #000000;
    border-radius: 16rpx;
    padding: 24rpx;
    box-shadow: 0 4rpx 16rpx rgba(255, 255, 255, 0.2);
    border: 2rpx solid #004676;

    .lang-title {
      font-size: 28rpx;
      color: rgba(255, 255, 255, 0.6);
      margin-bottom: 24rpx;
      padding: 0 24rpx;
    }

    .lang-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 24rpx;
      border-radius: 12rpx;
      margin-bottom: 8rpx;
      transition: all 0.3s;

      &:active {
        opacity: 0.8;
      }

      &.active {
        background-color: #031f41;

        .name {
          color: #f6c691;
        }

        .star {
          opacity: 1;

          &::before {
            color: #f6c691;
          }
        }
      }

      .left {
        display: flex;
        align-items: center;

        .flag {
          width: 36rpx;
          height: 36rpx;
          margin-right: 16rpx;
          object-fit: fill;
          display: block;
          flex-shrink: 0;
        }

        .name {
          font-size: 28rpx;
          color: rgba(255, 255, 255, 0.8);
        }
      }

      .star {
        width: 36rpx;
        height: 36rpx;
        position: relative;
        display: inline-block;
        opacity: 0.3;
        transition: all 0.3s;

        &::before {
          content: '';
          position: absolute;
          left: 50%;
          top: 50%;
          width: 100%;
          height: 100%;
          transform: translate(-50%, -50%) rotate(35deg);
          background: currentColor;
          clip-path: polygon(50% 0%, 61% 35%, 98% 35%, 68% 57%, 79% 91%, 50% 70%, 21% 91%, 32% 57%, 2% 35%, 39% 35%);
          color: rgba(255, 255, 255, 0.6);
        }
      }

      .name {
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-size: 28rpx;
      }

      // 长文本语言的特殊处理
      &[lang="de"],
      &[lang="nl"],
      &[lang="ru"],
      &[lang="el"] {
        .name {
          font-size: 24rpx;
        }
      }

      // 阿拉伯语的特殊处理
      &[lang="ar"] {
        flex-direction: row;
        text-align: left;

        .left {
          flex-direction: row;
        }
      }
    }

    /* 为阿拉伯语添加特殊样式 */
    [lang="ar"] & {
      left: 24rpx;
      right: auto;
      text-align: right;

      .lang-item {
        flex-direction: row-reverse;
      }
    }

    /* 为长文本语言调整样式 */
    [lang="de"] &,
    [lang="nl"] &,
    [lang="el"] &,
    [lang="ru"] & {
      width: 440rpx;

      .lang-item .name {
        font-size: 26rpx;
      }
    }

    [lang="el"] & {
      .lang-item .name {
        font-size: 24rpx;
      }

      .grid-item text {
        font-size: 22rpx;
      }
    }
  }
}
</style>