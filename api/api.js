import fetch from "@/utils/http";

/*获取配置信息 */
export const getBlockchainConfig = () => {
  return fetch.get(`/api/Config/getChinaInfo`)
}
/*登录 */
export const login = (params) => {
  return fetch.post("/api/user/login", params);
};
/*获取用户信息 */
export const getMemberInfo = () => {
  return fetch.post(`/api/Member/getUserInfo`)
}

/* 抽奖商品接口 */
export const qGetPrizes = (params) => {
  return fetch.post("/api/Lottery/getPrizes", params);
};
/* 执行抽奖接口 */
export const qDrawLottery = (params) => {
  return fetch.post("/api/Lottery/drawLottery", params);
};
/* 抽奖记录接口 */
export const qgetLotteryRecords = (params) => {
  return fetch.post("/api/Lottery/getLotteryRecords", params);
};
/* 当前奖池接口 */
export const qawardPoolStatus = (params) => {
  return fetch.post("/api/Member/awardPoolStatus", params);
};
/* 新手指南接口 */
export const qlistNewcomer = (params) => {
  return fetch.post("/api/Newcomer/list", params);
};
/* 今日动态接口 */
export const qtodayEvent = (params) => {
  return fetch.post("/api/Member/todayEvent", params);
};
/* 投入资金接口 */
export const qinvest = (params) => {
  return fetch.post("/api/Pool/invest", params);
};
/* 获取奖池信息接口 */
export const qinfo = (params) => {
  return fetch.post("/api/Pool/info", params);
};
/* 获取用户奖池持有信息接口 */
export const quserInfo = (params) => {
  return fetch.post("/api/Pool/userInfo", params);
};
/* 退出资金接口 */
export const qwithdraw = (params) => {
  return fetch.post("/api/Pool/withdraw", params);
};
/* 提现配置信息 */
export const qconfig = (params) => {
  return fetch.get("/api/Withdraw/config", params);
};
/* 提现接口 */
export const qapply = (params) => {
  return fetch.post("/api/Withdraw/apply", params);
};
/* 转账接口 */
export const qtransfer = (params) => {
  return fetch.post("/api/Withdraw/transfer", params);
};
/* 充值接口 */
export const qrecharge = (params) => {
  return fetch.post("/api/Withdraw/recharge", params);
};
/* 异步充值接口 */
export const qasyncRecharge = (params) => {
  return fetch.post("/api/Withdraw/asyncRecharge", params);
};
/* 报单详情接口 */
export const qdetail = (params) => {
  return fetch.post("/api/Product/detail", params);
};
/* 激活报单接口 */
export const qsubmit = (params) => {
  return fetch.post("/api/Order/submit", params);
};
/* 分红界面信息接口 */
export const qbonus_info = (params) => {
  return fetch.post("/api/Member/bonus_info", params);
};