// 固定容器样式
.fixed-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  min-height: 100vh;
  background-color: #000000;
}

// 页面基础样式
.page-container {
  @extend .fixed-container;
  padding: 20rpx;
  
  &.no-padding {
    padding: 0;
  }
}

// 页面切换动画
.page-transition {
  transition: all 0.3s ease;
  
  &-enter {
    opacity: 0;
    transform: translateX(100%);
  }
  
  &-leave {
    opacity: 0;
    transform: translateX(-100%);
  }
}

// 骨架屏加载样式
@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.skeleton {
  background: linear-gradient(90deg, 
    rgba(255, 255, 255, 0.05) 25%, 
    rgba(255, 255, 255, 0.1) 37%, 
    rgba(255, 255, 255, 0.05) 63%
  );
  background-size: 200% 100%;
  animation: shimmer 1.4s ease infinite;
}

// 默认占位样式
.placeholder {
  opacity: 0.3;
  color: rgba(255, 255, 255, 0.6);
} 