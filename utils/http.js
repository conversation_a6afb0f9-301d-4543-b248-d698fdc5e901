let BASE_URL;

// 1环境：https://ailkapi.tocoinweb3.com
// 2环境：https://ailkdapp.tocoinweb3.com

if (process.env.NODE_ENV === "development") {
  BASE_URL = "https://ailkapi.tocoinweb3.com";
} else if (process.env.NODE_ENV === "production") {
  BASE_URL = "https://ailkapi.tocoinweb3.com";
}

let address;
let token;

const requestInterceptor = (config) => {
  address = sessionStorage.getItem("token")
    ? sessionStorage.getItem("token")
    : "";
  token = sessionStorage.getItem("token")
    ? sessionStorage.getItem("token")
    : "";

  // 添加请求拦截逻辑，在这里可以对请求进行处理，例如添加请求头、签名等
  config.header = {
    // address,
    token,
    ...config.header,
  };
  return config;
};

const responseInterceptor = (response) => {
  // 添加响应拦截逻辑
  // 在这里可以对响应进行处理，例如处理错误码、数据解析等

  if (response.statusCode === 200) {
    // HTTP状态码
    // if (response.data.code === 0) {
    // 业务状态码
    return response.data;
    // } else {
    //   uni.hideLoading();
    //   uni.showToast({
    //     title: response.data.msg,
    //     icon: "error",
    //   });
    //   throw new Error(
    //     "Request failed with status code:" +
    //       response.statusCode +
    //       ", msg:" +
    //       response.msg
    //   );
    // }
  } else {
    throw new Error(
      "Request failed with status code:" +
        response.statusCode +
        ", msg:" +
        response.msg
    );
  }
};


const request = (config, isLoading) => {
  if (isLoading) {
    uni.showLoading({
      mask: true,
    });
  }
  const requestConfig = {
    ...config,
    header: requestInterceptor(config).header,
    url: BASE_URL + config.url,
  };

  return new Promise((resolve, reject) => {
    uni.request({
      ...requestConfig,
      success: (res) => {
        try {
          resolve(responseInterceptor(res));
        } catch (error) {
          console.log(error);
          reject(error);
        } finally {
          uni.hideLoading();
        }
      },
      fail: (err) => {
        reject(err);
        uni.hideLoading();
      },
    });
  });
};

export default {
  get: (url, params = {}, isLoading = true) => {
    const config = {
      url,
      method: "GET",
      data: params,
    };
    return request(config, isLoading);
  },
  post: (url, data = {}, isLoading = true) => {
    const config = {
      url,
      method: "POST",
      data,
    };
    return request(config, isLoading);
  },
};