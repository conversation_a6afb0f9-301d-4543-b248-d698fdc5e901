// import i18n from '@/locales/index.js'
// export const updateTabBar = () => {
//     const { t } = i18n.global // 兼容 Vue2/Vue3
//     const tabMap = [
//         { index: 0, key: '_Ailk.thome' },
//         { index: 1, key: '_Ailk.tlottery' },
//         { index: 2, key: '_Ailk.tdividend' },
//         { index: 3, key: '_Ailk.torder' },
//         { index: 4, key: '_Ailk.tmine' }
//     ]

//     tabMap.forEach(({ index, key }) => {
//         uni.setTabBarItem({
//             index,
//             text: t(key)
//         })
//     })
// }

// utils/tabbar.js
export const updateTabBar = (appContext) => {
    const { t } = appContext.config.globalProperties.$i18n.global
    const tabMap = [
        { index: 0, key: '_Ailk.thome' },
        { index: 1, key: '_Ailk.tlottery' },
        { index: 2, key: '_Ailk.tdividend' },
        { index: 3, key: '_Ailk.torder' },
        { index: 4, key: '_Ailk.tmine' }
    ]

    tabMap.forEach(({ index, key }) => {
        uni.setTabBarItem({
            index,
            text: t(key)
        })
    })
}

// App.vue 调用时传递上下文
import { getCurrentInstance } from 'vue'
const { appContext } = getCurrentInstance()
updateTabBar(appContext)