import _web3 from "web3";
import { login } from "../../api/api";

// 钱包工具
class bsc {
  // static walletAddress = false; // 当前用户地址
  static web3;
  // 检测当前网络环境
  network() {
    return ethereum.networkVersion;
  }
  // 初始化wbe3
  init() {
    if (this.web3) {
      return;
    }
    if (typeof _web3 !== "undefined") {
      // this.web3 = new _web3(web3.currentProvider);
      this.web3 = new _web3(window.ethereum);
    } else {
      this.web3 = new _web3(
        new Web3.providers.HttpProvider("http://localhost:8080/")
      );
    }
  }
  // 创建合约对象
  // obj： {
  // 	abi: [],
  // 	contract: ''
  // }
  async Contract(obj) {
    if (this[obj.name]) {
      return;
    }
    if (!this.web3) {
      await this.init();
    }
    if (typeof obj.abi === "string") {
      var abi = JSON.parse(obj.abi);
    } else {
      var { abi } = obj;
    }
    this[obj.name] = new this.web3.eth.Contract(abi, obj.contract);
  }
  // 延时时间
  delay(ms) {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }
  // 根据hash查询交易是否成功
  async queryPayment(resolve, reject, transactionHash) {
    while (1) {
      let state = await this.web3.eth.getTransactionReceipt(transactionHash);
      console.log("state", state);
      await this.delay(1000);
      if (state == null) {
        console.log("continue");
        continue;
      }
      if (state.status == true) {
        console.log("success");
        resolve(transactionHash);
        return;
      } else {
        console.log("fail");
        reject(transactionHash);
        return;
      }
    }
  }
  // 获取以太坊网络上的 Gas 价格
  async getGasPrice() {
    return await this.web3.eth.getGasPrice().then((_gasPrice) => {
      // console.log("_gasPrice--------->", _gasPrice);
      return _gasPrice;
    });
  }
  // 主币交易 bnb
  request(obj) {
    return new Promise(async (resolve, reject) => {
      if (obj.fromAddress) {
        var { fromAddress } = obj;
      } else {
        var fromAddress = await this.address();
      }
      const quantity = obj.quantity * 1000000000000000000;
      ethereum
        .request({
          method: "eth_sendTransaction",
          params: [
            {
              from: fromAddress, // 付款方
              to: obj.toAddress, // 收款方
              value: `0x${quantity.toString(16)}`, // 价格 16进制
              // gasPrice: '0x'+'0',	// 手续费 可以不设置但是不能过低
              // gasLimit: '0x'+'5208',	// 暂时不知道是什么东西
              // gas: '0x'+'33450'	// 手续费 同上
            },
          ],
        })
        .then((transactionHash) => {
          // 成功执行
          resolve(transactionHash);
        })
        .catch((error) => {
          // 失败执行
          reject();
        });
    });
  }
  // 连接钱包 initContract
  connect_purse() {
    return new Promise(async (resolve, reject) => {
      if (typeof window.ethereum === "undefined") {
        uni.showToast({
          icon: "none",
          title: "当前环境不可用",
        });
        this.init();
        reject();
      }
      const accounts = await ethereum
        .request({
          method: "eth_requestAccounts",
        })
        .catch((err) => {
          console.log("err", err);
          uni.showToast({
            icon: "none",
            title: "链接被拒绝",
          });
          reject();
        });
      if (accounts && accounts[0]) {
        // uni.showToast({
        //   icon: 'none',
        //   title: '链接成功',
        // });
        this.walletAddress = accounts[0];
        resolve(this.walletAddress);
      }
      // console.log("登录被拒绝", accounts);
    });
  }
  // 发起指定合约转账
  build_transfer(obj) {
    // 调用的合约对象 obj.name
    // 钱包地址 obj.fromAccount
    // 收款地址 obj.toAccount
    // 价格 obj.quantity
    // 合约地址 obj.contract
    // 合约abi obj.abi
    // if (obj.name1.indexOf('波场') > -1) {
    // } else {
    return new Promise(async (resolve, reject) => {
      // try {
      // debugger;
      // 指定的合约对象是否存在,不存在则去创建
      let walletAddress;
      if (!this[obj.name]) {
        await this.Contract(obj);
      }
      console.log(this.specified_decimals(obj));
      // 获取代币小数位
      const decimal = await this.specified_decimals(obj);
      // 计算价格

      let { quantity } = obj;
      quantity = BigInt(quantity) * BigInt(10 ** Number(decimal));
      // console.log(quan);
      // 没有指定的钱包地址则查询当前连接的钱包
      if (obj.fromAccount) {
        walletAddress = obj.fromAccount;
      } else {
        walletAddress = await this.address();
      }
      let that = this;
      console.log('参数2', quantity);
      /* transfer 方法用于在 ERC20 标准中实现代币的转移操作，
    允许用户将代币从一个地址转移到另一个地址 */
      try {
        const mehods_value = await this[obj.name].methods
          .transfer(obj.toAccount, quantity.toString())
          .send(
            {
              from: walletAddress,
            }
          );
        console.log(mehods_value);
        that.queryPayment(resolve, reject, mehods_value.transactionHash);
      } catch (error) {
        uni.hideLoading();
        uni.showToast({
          title: '充值失败',
          icon: 'none',
        })
        console.log(error);
      }
      // async function (error, transactionHash) {
      //   if (!error) {
      //     that.queryPayment(resolve, reject, transactionHash);
      //     console.log(transactionHash);
      //   } else {
      //     // 失败执行
      //     reject(error);
      //   }
      // }
      // } catch (err) {
      //   console.log('err>>',err);
      //   uni.showToast({
      //     title: '充值失败',
      //     icon: 'none',
      //     duration: 1500,
      //     mask: true,
      //   });
      //   uni.hideLoading();
      // }
    });
    // }
  }
  // 代币授权
  // 根据地址获取代币金额
  async authorize(obj) {
    // 调用的合约对象 obj.name
    // 钱包地址 obj.fromAccount
    // 收款地址 obj.toAccount
    // 价格 obj.quantity
    // 合约地址 obj.contract
    // 合约abi obj.abi
    return new Promise(async (resolve, reject) => {
      // 指定的合约对象是否存在,不存在则去创建
      let walletAddress;
      if (!this[obj.name]) {
        console.log("没有合约对象");
        await this.Contract(obj);
      } else {
        console.log("有合约对象");
      }

      // 没有指定的钱包地址则查询当前连接的钱包
      if (obj.fromAccount) {
        walletAddress = obj.fromAccount;
      } else {
        walletAddress = await this.address();
      }

      // 小数位
      const decimal = await this.specified_decimals(obj);
      // 计算价格
      let { quantity } = obj;
      if (decimal && decimal > 6) {
        quantity *= 1000000;
        for (var i = 0; i < decimal - 6; i++) {
          quantity += "0";
        }
      } else {
        let sb = 1;
        for (var i = 0; i < (decimal || 0); i++) {
          sb += "0";
        }
        quantity *= sb;
      }

      // 获取以太坊网络上的 Gas 价格
      let gasPrice = await this.getGasPrice();
      console.log("gasPrice", gasPrice);

      /* approve 方法是在用户或者其他智能合约准备进行
      代币转移之前调用的，用以授权所需的代币数量 */
      const obj1 = await this[obj.name].methods.approve(
        obj.toAccount,
        quantity.toString()
      );
      let gas = null;
      let that = this;
      // 估算调用智能合约中某个方法所需要的 gas 用量
      await obj1
        .estimateGas({
          from: walletAddress,
          gasPrice: gasPrice,
        })
        .then((gasAmount) => {
          console.log("Estimated gas amount:", gasAmount);
          gas = gasAmount;
        });
      obj1.send(
        {
          from: walletAddress,
          gas: gas,
          gasPrice: gasPrice,
        },
        async function (error, transactionHash) {
          if (!error) {
            // 成功执行时返回订单号
            that.queryPayment(resolve, reject, transactionHash);
          } else {
            // 失败执行
            reject(error);
          }
        }
      );
    });
  }
  // 获取奖励
  async getReward(obj) {
    // 调用的合约对象 obj.name
    // 合约地址 obj.contract
    // 合约abi obj.abi
    return new Promise(async (resolve, reject) => {
      // 指定的合约对象是否存在,不存在则去创建
      let walletAddress;
      if (!this[obj.name]) {
        await this.Contract(obj);
      }
      // 没有指定的钱包地址则查询当前连接的钱包
      if (obj.fromAccount) {
        walletAddress = obj.fromAccount;
      } else {
        walletAddress = await this.address();
      }

      /* getReward() 方法可能是用来获取某种奖励或奖励信息的智能合约函数 */
      await this[obj.name].methods.getReward().call(
        {
          from: walletAddress,
        },
        (error, result) => {
          if (!error) {
            resolve(result);
            return result;
          }
          reject(error);
        }
      );
    });
  }
  // 获取预售限购数量
  async getPresaleNum(obj) {
    // 调用的合约对象 obj.name
    // 合约地址 obj.contract
    // 合约abi obj.abi
    // console.log("获取商品传递参数", obj);
    return new Promise(async (resolve, reject) => {
      // 指定的合约对象是否存在,不存在则去创建
      if (!this[obj.name]) {
        await this.Contract(obj);
      }
      // let walletAddress;
      // if (obj.fromAccount) {
      //   walletAddress = obj.fromAccount;
      // } else {
      //   walletAddress = await this.address();
      // }
      let aa = await this[obj.name].methods.purchaseLimit().call();

      resolve(aa);
    });
  }
  async getPriceNum(obj) {
    // 调用的合约对象 obj.name
    // 合约地址 obj.contract
    // 合约abi obj.abi
    // console.log("获取商品传递参数", obj);
    return new Promise(async (resolve, reject) => {
      // 指定的合约对象是否存在,不存在则去创建
      if (!this[obj.name]) {
        await this.Contract(obj);
      }
      // let walletAddress;
      // if (obj.fromAccount) {
      //   walletAddress = obj.fromAccount;
      // } else {
      //   walletAddress = await this.address();
      // }
      let aa = await this[obj.name].methods.price().call();

      resolve(aa);
    });
  }
  async getminInvestmentAmount(obj) {
    return new Promise(async (resolve, reject) => {
      if (!this[obj.name]) {
        await this.Contract(obj);
      }
      let aa = await this[obj.name].methods
        .minInvestmentAmount()
        .call();
      resolve(aa);
    });
  }
  async invest(obj) {
    // 调用的合约对象 obj.name
    // 合约地址 obj.contract
    // 合约abi obj.abi
    console.log("获取商品传递参数", obj);
    return new Promise(async (resolve, reject) => {
      // 指定的合约对象是否存在,不存在则去创建
      // console.log(obj);
      if (!this[obj.name]) {
        await this.Contract(obj);
      }
      try {
        let gasPrice = await this.getGasPrice();
        console.log("gasPrice", gasPrice);
        let obj1 = await this[obj.name].methods.invest(
          obj.buy_num
        );
        let gas;
        await obj1
          .estimateGas({
            from: obj.fromAddress,
            gasPrice: gasPrice,
            value: obj.buy_num,
          })
          .then((gasAmount) => {
            console.log("Estimated gas amount:", gasAmount);
            gas = gasAmount;
          });
        let aa = await obj1.send({
          from: obj.fromAddress,
          gasPrice: parseInt(parseInt(gasPrice) * 1.2),
          gas: gas,
          value: obj.buy_num,
        });
        if (aa.transactionHash) {
          const formData = new FormData();
          formData.append("amount", obj.buy_num / 10 ** 18);
          formData.append("amount_type", "1");
          formData.append("asset_type", "4");// 入金是4
          formData.append("hash", aa.transactionHash);
          login("/api/Finance/equipBuy", formData);
        }
        this.queryPayment(resolve, reject, aa.transactionHash);
      } catch (error) {
        console.log("错误", error);
        reject(error);
      }
    });
  }
  async buyPointer(obj) {
    // 调用的合约对象 obj.name
    // 合约地址 obj.contract
    // 合约abi obj.abi
    console.log("获取商品传递参数", obj);
    return new Promise(async (resolve, reject) => {
      // 指定的合约对象是否存在,不存在则去创建
      // console.log(obj);
      if (!this[obj.name]) {
        await this.Contract(obj);
      }
      try {
        let gasPrice = await this.getGasPrice();
        console.log("gasPrice", gasPrice);
        let obj1 = await this[obj.name].methods.buy(
          obj.up_address,
          obj.buy_num
        );
        let gas;
        await obj1
          .estimateGas({
            from: obj.fromAddress,
            gasPrice: gasPrice,
            value: obj.value,
          })
          .then((gasAmount) => {
            console.log("Estimated gas amount:", gasAmount);
            gas = gasAmount;
          });
        let aa = await obj1.send({
          from: obj.fromAddress,
          gasPrice: parseInt(parseInt(gasPrice) * 1.2),
          gas: gas,
          value: obj.value,
        });
        if (aa.transactionHash) {
          const formData = new FormData();
          formData.append("amount", obj.value / 10 ** 18);
          formData.append("amount_type", "1");
          formData.append("asset_type", "12");// 入金是4
          formData.append("hash", aa.transactionHash);
          login("/api/Finance/equipBuy", formData);
        }
        this.queryPayment(resolve, reject, aa.transactionHash);
      } catch (error) {
        console.log("错误", error);
        reject(error);
      }
    });
  }
  getClaimAccount(obj) {
    return new Promise(async (resolve, reject) => {
      // 指定的合约对象是否存在,不存在则去创建
      // console.log(obj);
      if (!this[obj.name]) {
        await this.Contract(obj);
      }
      let gasPrice = await this.getGasPrice();
      console.log("gasPrice", gasPrice);
      let obj1 = await this[obj.name].methods.claim(obj.withdraw_num);
      let gas;
      await obj1
        .estimateGas({
          from: obj.fromAddress,
          gasPrice: gasPrice,
        })
        .then((gasAmount) => {
          console.log("Estimated gas amount:", gasAmount);
          gas = gasAmount;
        });
      let aa = await obj1.send({
        from: obj.fromAddress,
        gasPrice: parseInt(parseInt(gasPrice) * 1.2),
        gas: gas,
      });
      resolve(aa);
    });
  }
  getUserBuyList(obj) {
    return new Promise(async (resolve, reject) => {
      if (!this[obj.name]) {
        await this.Contract(obj);
      }
      let aa = await this[obj.name].methods
        .getBuyList()
        .call({ from: obj.fromAddress });
      resolve(aa);
    });
  }
  getRewardList(obj) {
    return new Promise(async (resolve, reject) => {
      if (!this[obj.name]) {
        await this.Contract(obj);
      }
      let aa = await this[obj.name].methods
        .getRewardList()
        .call({ from: obj.fromAddress });
      resolve(aa);
    });
  }
  // XMT价格
  async getXMTPrice(obj) {
    // 调用的合约对象 obj.name
    // 合约地址 obj.contract
    // 合约abi obj.abi
    return new Promise(async (resolve, reject) => {
      // 指定的合约对象是否存在,不存在则去创建
      let walletAddress;
      if (!this[obj.name]) {
        await this.Contract(obj);
      }
      // 没有指定的钱包地址则查询当前连接的钱包
      if (obj.fromAccount) {
        walletAddress = obj.fromAccount;
      } else {
        walletAddress = await this.address();
      }
      let _token0, price;
      await this[obj.name].methods
        .token0()
        .call()
        .then((res) => {
          // console.log("token0", res);
          _token0 = res;
        });

      /* getReserves() 方法可能是用来获取某种奖励或奖励信息的智能合约函数 */
      await this[obj.name].methods
        .getReserves()
        .call()
        .then((reserves) => {
          // console.log(reserves);

          // 假设 token0 是以太币（ETH）
          if (_token0.toUpperCase() == obj.WETHAddress.toUpperCase()) {
            price =
              reserves._reserve0.toString() / reserves._reserve1.toString();
          } else {
            price =
              reserves._reserve1.toString() / reserves._reserve0.toString();
          }
          // console.log("price", price);
          resolve(price);
        })
        .catch((error) => {
          reject();
          console.error("Error fetching reserves:", error);
        });
    });
  }
  // 领取奖励
  claim(obj) {
    // 调用的合约对象 obj.name
    // 合约地址 obj.contract
    // 合约abi obj.abi
    return new Promise(async (resolve, reject) => {
      // 指定的合约对象是否存在,不存在则去创建
      let walletAddress;
      if (!this[obj.name]) {
        await this.Contract(obj);
      }
      // 没有指定的钱包地址则查询当前连接的钱包
      if (obj.fromAccount) {
        walletAddress = obj.fromAccount;
      } else {
        walletAddress = await this.address();
      }

      // 获取以太坊网络上的 Gas 价格
      let gasPrice = await this.getGasPrice();
      console.log("gasPrice", gasPrice);

      /* claim() 方法是用来领取用户在智能合约中积累的奖励或者权益 */
      const obj1 = await this[obj.name].methods.claim();
      let that = this;
      let gas = null;

      // 估算调用智能合约中某个方法所需要的 gas 用量
      await obj1
        .estimateGas({
          from: walletAddress,
          gasPrice: gasPrice,
        })
        .then((gasAmount) => {
          console.log("Estimated gas amount:", gasAmount);
          gas = gasAmount;
        });
      obj1.send(
        {
          from: walletAddress,
          gas: gas,
          gasPrice: gasPrice,
        },
        async function (error, data) {
          console.log("claim,error,data", error, data);
          if (!error) {
            that.queryPayment(resolve, reject, data);
          } else {
            // 失败执行
            reject(error);
          }
        }
      );
    });
  }
  // 获取用户信息
  async getUserInfo(obj) {
    console.log("获取商品传递参数", obj);
    return new Promise(async (resolve, reject) => {
      // 指定的合约对象是否存在,不存在则去创建
      if (!this[obj.name]) {
        await this.Contract(obj);
      }
      // let walletAddress;
      // if (obj.fromAccount) {
      //   walletAddress = obj.fromAccount;
      // } else {
      //   walletAddress = await this.address();
      // }
      // console.log(********, this[obj.name]);
      console.log(obj);
      let aa = await this[obj.name].methods.getUserList().call({
        from: obj.fromAddress,
      });
      resolve(aa);
    });
  }
  async getTransfer(obj) {
    return new Promise(async (resolve, reject) => {
      // 指定的合约对象是否存在,不存在则去创建
      if (!this[obj.name]) {
        await this.Contract(obj);
      }
      let aa = await this[obj.name].methods
        .transfer()
        .call({ to: obj.ToAddress, value: obj.value });
      resolve(aa);
    });
  }
  // 获取商品列表
  async getProductList(obj) {
    console.log("获取商品传递参数", obj);
    return new Promise(async (resolve, reject) => {
      // 指定的合约对象是否存在,不存在则去创建
      if (!this[obj.name]) {
        await this.Contract(obj);
      }
      // let walletAddress;
      // if (obj.fromAccount) {
      //   walletAddress = obj.fromAccount;
      // } else {
      //   walletAddress = await this.address();
      // }
      let aa = await this[obj.name].methods
        .productList({
          from: obj.fromAddress,
        })
        .call();
      resolve(aa);
    });
  }
  // 获取nft列表
  getNFTList(obj) {
    return new Promise(async (resolve, reject) => {
      if (!this[obj.name]) {
        await this.Contract(obj);
      }
      let ids = await this[obj.name].methods.getIdsByOwner().call({
        from: obj.fromAccount,
      });
      // console.log(ids);
      // let imgArr = [];
      let Data_obj = {};
      let fetchPromises = ids.map(async (element) => {
        console.log(element);
        if (element === 0) {
          return;
        }
        let aa = await this[obj.name].methods.tokenURI(element).call({
          from: obj.fromAccount,
        });
        return fetch(aa)
          .then((response) => response.json())
          .then((data) => {
            // console.log(aa);
            Data_obj[element] = data.image;
            // imgArr.push(data.image);
          })
          .catch((error) => {
            console.error("获取数据失败:", error);
          });
      });

      Promise.all(fetchPromises).then(() => {
        // console.log(Data_obj); // 在所有fetch请求完成后输出imgArr数组
        // let combinedData = aa.map((id, index) => {
        //   let obj = {}
        //   obj[id] = aa[index];
        //   obj[id] = imgArr[index]
        // })
        // console.log(combinedData);
        resolve(Data_obj);
      });
    });
  }
  // 铸币
  buy(obj) {
    // 价格 obj.qu
    // 合约地址 obj.contract
    // 合约abi obj.abi
    return new Promise(async (resolve, reject) => {
      // 指定的合约对象是否存在,不存在则去创建
      if (!this[obj.name]) {
        await this.Contract(obj);
      }
      let walletAddress;
      if (obj.fromAccount) {
        walletAddress = obj.fromAccount;
      } else {
        walletAddress = await this.address();
      }
      /* buy 方法通常用于智能合约中的购买操作，用于处理用户
      的购买请求，并执行相应的逻辑以完成购买过程 */
      try {
        // 获取以太坊网络上的 Gas 价格
        let gasPrice = await this.getGasPrice();
        let obj1 = await this[obj.name].methods.buy(obj.level);
        // 估算调用智能合约中某个方法所需要的 gas 用量
        let gas;
        await obj1
          .estimateGas({
            from: walletAddress,
            gasPrice: gasPrice,
            value: obj.price,
          })
          .then((gasAmount) => {
            console.log("Estimated gas amount:", gasAmount);
            gas = gasAmount;
          });

        let aa = await obj1.send({
          from: walletAddress,
          value: obj.price,
          gas: gas,
          gasPrice: parseInt(parseInt(gasPrice) * 1.2),
        });
        if (aa.transactionHash) {
          const formData = new FormData();
          formData.append("price", obj.price / 10 ** 18);
          formData.append("level_type", obj.level);
          formData.append("transfer_tx", aa.transactionHash);
          login("/api/Order/submitOrder", formData);
        }

        this.queryPayment(resolve, reject, aa.transactionHash);
        // resolve(aa.transactionHash);
        // console.log("buy send返回值", aa, aa.blockHash);
      } catch (error) {
        reject(error);
      }
    });
  }

  // 查询是否已购买
  getpurchased(obj) {
    return new Promise(async (resolve, reject) => {
      // 指定的合约对象是否存在,不存在则去创建
      let walletAddress;
      if (!this[obj.name]) {
        await this.Contract(obj);
      }
      // 没有指定的钱包地址则查询当前连接的钱包
      if (obj.fromAccount) {
        walletAddress = obj.fromAccount;
      } else {
        walletAddress = await this.address();
      }

      /* getpurchased() 方法通常用于智能合约中的购买记录查询操作，
      用户可以使用它来查询已经购买的商品或服务的相关信息 */
      await this[obj.name].methods.getpurchased().call(
        {
          from: walletAddress,
        },
        (error, result) => {
          if (!error) {
            resolve(result);
            return result;
          }
          reject(error);
        }
      );
    });
  }

  // 查询是否有余量
  getRemainingQty(obj) {
    return new Promise(async (resolve, reject) => {
      // 指定的合约对象是否存在,不存在则去创建
      let walletAddress;
      if (!this[obj.name]) {
        await this.Contract(obj);
      }
      // 没有指定的钱包地址则查询当前连接的钱包
      if (obj.fromAccount) {
        walletAddress = obj.fromAccount;
      } else {
        walletAddress = await this.address();
      }

      /* getRemainingQty() 方法通常用于智能合约中的剩余数量查询操作，
      用户可以使用它来获取特定商品或服务的剩余数量信息 */
      await this[obj.name].methods.getRemainingQty().call(
        {
          from: walletAddress,
        },
        (error, result) => {
          if (!error) {
            resolve(result);
            return result;
          }
          reject(error);
        }
      );
    });
  }

  // 查询是否已提款
  async isWithdrew(obj) {
    return new Promise(async (resolve, reject) => {
      // 指定的合约对象是否存在,不存在则去创建
      let walletAddress;
      if (!this[obj.name]) {
        await this.Contract(obj);
      }
      // 没有指定的钱包地址则查询当前连接的钱包
      if (obj.fromAccount) {
        walletAddress = obj.fromAccount;
      } else {
        walletAddress = await this.address();
      }

      /* isWithdrew() 方法通常用于智能合约中的提取状态查询操作，
      用户可以使用它来判断特定操作是否已经执行了提取操作 */
      await this[obj.name].methods
        .isWithdrew()
        .call({ from: walletAddress }, (error, result) => {
          if (!error) {
            resolve(result);
            return result;
          }
          reject(error);
        });
    });
  }

  // 查询是否可以提款
  async isCanWithdraw(obj) {
    return new Promise(async (resolve, reject) => {
      // 指定的合约对象是否存在,不存在则去创建
      let walletAddress;
      if (!this[obj.name]) {
        await this.Contract(obj);
      }
      // 没有指定的钱包地址则查询当前连接的钱包
      if (obj.fromAccount) {
        walletAddress = obj.fromAccount;
      } else {
        walletAddress = await this.address();
      }

      /* canWithdraw 方法通常用于智能合约中的提取操作条件查询操作，
      用户可以使用它来判断特定操作是否可以执行提取操作 */
      await this[obj.name].methods
        .canWithdraw(walletAddress)
        .call({ from: walletAddress }, (error, result) => {
          if (!error) {
            resolve(result);
            return result;
          }
          reject(error);
        });
    });
  }

  // 提款
  async withdraw(obj) {
    // 调用的合约对象 obj.name
    // 合约地址 obj.contract
    // 合约abi obj.abi
    return new Promise(async (resolve, reject) => {
      // 指定的合约对象是否存在,不存在则去创建
      let walletAddress;
      if (!this[obj.name]) {
        await this.Contract(obj);
      }
      // 没有指定的钱包地址则查询当前连接的钱包
      if (obj.fromAccount) {
        walletAddress = obj.fromAccount;
      } else {
        walletAddress = await this.address();
      }

      // 获取以太坊网络上的 Gas 价格
      let gasPrice = await this.getGasPrice();
      console.log("gasPrice", gasPrice);

      /* withdraw 方法通常用于智能合约中的提取操作，用户可以
      使用它来执行提取操作，从智能合约中取出资金或者其他资源 */
      const obj1 = await this[obj.name].methods.withdraw();
      let that = this;
      let gas = null;

      // 估算调用智能合约中某个方法所需要的 gas 用量
      await obj1
        .estimateGas({
          from: walletAddress,
          gasPrice: gasPrice,
        })
        .then((gasAmount) => {
          console.log("Estimated gas amount:", gasAmount);
          gas = gasAmount;
        });

      obj1.send(
        {
          from: walletAddress,
          gas: gas,
          gasPrice: gasPrice,
        },
        async function (error, data) {
          if (!error) {
            that.queryPayment(resolve, reject, data);
          } else {
            // 失败执行
            reject(error);
          }
        }
      );
    });
  }

  // 查询是否有下一次
  async getNextStage(obj) {
    return new Promise(async (resolve, reject) => {
      console.log("getNextStage params", obj);
      // 指定的合约对象是否存在,不存在则去创建
      let walletAddress;
      if (!this[obj.name]) {
        await this.Contract(obj);
      }
      // 没有指定的钱包地址则查询当前连接的钱包
      if (obj.fromAccount) {
        walletAddress = obj.fromAccount;
      } else {
        walletAddress = await this.address();
      }

      /* canWithdraw 方法通常用于智能合约中的提取操作条件查询操作，
        用户可以使用它来判断特定操作是否可以执行提取操作 */
      await this[obj.name].methods
        .getNextStage()
        .call({ from: walletAddress }, (error, result) => {
          if (!error) {
            resolve(result);
            return result;
          }
          reject(error);
        });
    });
  }

  // 查询授权金额 allowance
  async queryingAuthorizationAmount(obj, carry) {
    // 调用的合约对象 obj.name
    // 钱包地址 obj.address
    // 收款地址 obj.collection_address
    // 合约地址 obj.contract
    // 合约abi obj.abi
    let walletAddress;
    // 指定的合约对象是否存在,不存在则去创建
    if (!this[obj.name]) {
      await this.Contract(obj);
    }
    if (carry) {
      const decimals = await this.specified_decimals(obj);
      var sb = 1;
      for (let i = 0; i < decimals; i++) {
        sb += "0";
      }
    }
    // 没有指定的钱包地址则查询当前连接的钱包
    if (obj.address) {
      walletAddress = obj.address;
    } else {
      walletAddress = await this.address();
    }

    /* allowance 方法通常用于智能合约中的代币授权额度查询操作，
    用户可以使用它来查询某个地址对于合约的代币的授权额度 */
    const res = await this[obj.name].methods
      .allowance(walletAddress, obj.collection_address)
      .call(
        {
          from: walletAddress,
        },
        (error, result) => {
          if (!error) {
            return result;
          }
          return error;
        }
      );
    if (carry) {
      return res / sb;
    }
    return res;
  }

  // 查询以太币余额
  async balance(address, carry) {
    let num;
    let walletAddress;
    // 没有指定的钱包地址则查询当前连接的钱包
    if (address) {
      walletAddress = address;
    } else {
      walletAddress = await this.address();
    }
    await this.web3.eth.getBalance(walletAddress).then((n) => {
      num = n;
    });
    if (carry) {
      return num / 1000000000000000000;
    }
    return num;
  }

  // 查询指定代币余额
  async specified_balance(obj, carry) {

    let num;
    let walletAddress;
    // 指定的合约对象是否存在,不存在则去创建
    if (!this[obj.name]) {
      await this.Contract(obj);
    }
    if (carry) {
      const decimals = await this.specified_decimals(obj);
      var sb = 1;
      for (let i = 0; i < decimals; i++) {
        sb += "0";
      }
    }
    // 没有指定的钱包地址则查询当前连接的钱包
    if (obj.address) {
      walletAddress = obj.address;
    } else {
      walletAddress = await this.address();
    }
    /* balanceOf 方法通常用于智能合约中的代币余额查询操作，
    用户可以使用它来查询自己或其他地址在合约中的代币余额 */
    await this[obj.name].methods.balanceOf(walletAddress).call(
      {
        from: walletAddress,
      },
      (error, result) => {
        if (!error) {
          num = result;
        } else {
          console.log(error);
        }
      }
    );
    if (carry) {
      return num / sb;
    }
    return num;
  }

  // 查询代币小数位
  async specified_decimals(obj) {
    // 调用的合约对象 obj.name
    // 钱包地址 obj.fromAccount
    // 合约地址 obj.contract
    // 合约abi obj.abi
    // console.log(obj)
    let walletAddress;
    let num;
    // 指定的合约对象是否存在,不存在则去创建
    if (!this[obj.name]) {
      await this.Contract(obj);
    }
    // 没有指定的钱包地址则查询当前连接的钱包
    if (obj.address) {
      walletAddress = obj.address;
    } else {
      walletAddress = await this.address();
    }
    // console.log(this[obj.name].methods.decimals().call({ from: walletAddress }));
    /* decimals() 方法通常用于智能合约中的代币信息查询操作，
    用户可以使用它来查询代币的小数位数，以确定代币的精度 */
    num = await this[obj.name].methods.decimals().call();
    return num;
  }

  // 查询指定代币 tokenId
  async specified_token(obj) {
    // 调用的合约对象 obj.name
    // 钱包地址 obj.fromAccount
    // 合约地址 obj.contract
    // 合约abi obj.abi

    let num;
    let walletAddress;
    // 指定的合约对象是否存在,不存在则去创建
    if (!this[obj.name]) {
      await this.Contract(obj);
    }
    // 没有指定的钱包地址则查询当前连接的钱包
    if (obj.address) {
      walletAddress = obj.address;
    } else {
      walletAddress = await this.address();
    }

    /* tokenOfOwnerByIndex 方法通常用于智能合约中的代币所有者查询操作，
    用户可以使用它来查询指定地址拥有的代币标识符 */
    await this[obj.name].methods
      .tokenOfOwnerByIndex(walletAddress, obj.index)
      .call(
        {
          from: walletAddress,
        },
        (error, result) => {
          if (!error) {
            num = result;
          } else {
            console.log(error);
          }
        }
      );
    return num;
  }

  // nft转移
  // 获取代币名
  async get_name() {
    let name;
    let walletAddress;
    if (!this[obj.name]) {
      await this.Contract(obj);
    }

    // 没有指定的钱包地址则查询当前连接的钱包
    if (obj.address) {
      walletAddress = obj.address;
    } else {
      walletAddress = await this.address();
    }

    /* name() 方法用于查询智能合约中代币的名称，以便用户或其他智能合约使用 */
    this[obj.name].methods.name().call(
      {
        from: walletAddress,
      },
      (error, result) => {
        if (!error) {
          name = result;
        } else {
          console.log(error);
        }
      }
    );
    return name;
  }

  // nft转让
  async build_safe(obj) {
    // 调用的合约对象 obj.name
    // 钱包地址 obj.fromAccount
    // 收款地址 obj.toAccount
    // tokenId obj.tokenId
    // 合约地址 obj.contract
    // 合约abi obj.abi

    // 指定的合约对象是否存在,不存在则去创建
    let walletAddress;
    if (!this[obj.name]) {
      await this.Contract(obj);
    }
    // 没有指定的钱包地址则查询当前连接的钱包
    if (obj.fromAccount) {
      walletAddress = obj.fromAccount;
    } else {
      walletAddress = await this.address();
    }
    let that = this;

    /* safeTransferFrom 方法用于在 ERC721 标准中实现
    代币的安全转移操作，确保在转移过程中代币的安全性 */
    this[obj.name].methods
      .safeTransferFrom(walletAddress, obj.toAccount, obj.tokenId)
      .send(
        {
          from: walletAddress,
        },
        async function (error, transactionHash) {
          if (!error) {
            // 成功执行，返回交易号
            that.queryPayment(resolve, reject, transactionHash);
          } else {
            // 失败执行
            obj.fail && obj.fail(error);
          }
          // 无论如何都执行
          obj.whether && obj.whether();
        }
      );
  }

  // 获取当前连接钱包地址，没有的话尝试登录获取
  async address() {
    if (this.walletAddress) {
      return this.walletAddress;
    }
    await this.connect_purse();
    // console.log(this.walletAddress);
    return this.walletAddress;
  }

  // 请求切换到指定网络
  async network(obj) {
    console.log(obj);
    ethereum.request({
      method: "wallet_addEthereumChain", // Metamask的api名称
      params: [
        {
          chainId: `0x${obj.chainId.toString(16)}`, // 网络id，16进制的字符串
          chainName: obj.chainName, // 添加到钱包后显示的网络名称
          rpcUrls: [
            obj.host, // rpc地址
          ],
          blockExplorerUrls: [
            obj.blockExplorerUrl, // 网络对应的区块浏览器
          ],
          nativeCurrency: {
            // 网络主币的信息
            name: obj.symbol,
            symbol: obj.symbol,
            decimals: obj.decimals,
          },
        },
      ],
    });
  }

  // 签名加签
  async signaddress(address) {
    return this.web3.eth.personal.sign("", address, "test password!");
  }

  // 放在App.vue
  // ethereum.on('networkChanged', (networkIDstring) => {
  // 	//一旦切换网络这里就会执行
  // })
  // ethereum.on("accountsChanged", (accounts) => {
  // 	//一旦切换账号这里就会执行
  // });
}
export default new bsc();
