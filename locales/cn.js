export default {
  header: {
    'lang-title': '选择语言',
    'switch-success': '已切换到{lang}'
  },
  _Ailk: {
    thome: "首页",
    tlottery: "抽奖",
    tdividend: "分红",
    torder: "报单区",
    tmine: "我的",

    welcome: '欢迎来到Ailk，',
    guide: '新手指南请查看学习',
    quick_access: '快捷入口',
    lottery: '抽奖',
    dividend: '分红领取',
    order: '报单区',
    today_dynamic: '今天动态',
    pool_total: '奖池总量：',
    my_shares: '我的股数：',
    current_ratio: '股(当前占比',
    recommend_progress: '推荐进度：',
    target: '人 / 目标',
    ren: '人',
    remaining_time: '剩余时间：',
    beginner_guide: '新手指南',
    loading: '加载中...',
    no_more_data: '没有更多数据了',

    consumption_amount: '消费金额',
    discount_amount: '折扣金额',
    enter_consumption: '请输入消费金额',
    enter_discount: '请输入折扣金额',
    first_prize: '一等奖 = 全免消费额',
    second_prize: '二等奖 = 50%消费金额',
    third_prize: '三等奖 = 折扣金额',
    draw: '抽奖',
    pool_status: '奖池当前状态',
    current_balance: '当前奖池余额：',
    total_shares: '当前奖池总股数：',
    strand: '股',
    per_share_value: '每股价值：',
    invest: '入股',
    withdraw: '撤股',
    my_records: '我的抽奖记录',
    time: '时间',
    result: '结果',
    reward: '奖励',

    my_total_dividend: '我的总分红：',
    time_travel_betting: '(穿越+对赌)',
    today_available: '今天可领取：',
    claim: '领取',
    my_hshares: '我的分红份额',
    cross_pool: '穿越池',
    share: '份',
    bet_pool: '对赌池',
    notunlocked: '未解锁',
    unlock: '解锁',
    dividend_details: '分红明细',
    cross_pool_rule: '穿越池分红规则：每日释放',
    bet_pool_rule: '对赌池分红规则：任务成功者每日共享',
    cross_pool_investment: '穿越池投入区域',
    current_zbalance: '当前池中余额：',
    my_investment: '我的穿越投入：',
    enter_amount: '输入投入金额',
    tinvest: '投入',
    platform_fee: '（收取3%平台手续费）',
    claim_records: '分红领取记录',
    type: '类型',

    current_level: '我的当前等级：',
    referrer: '我的推荐人：',
    bound: '已绑定',
    next_level: '等级：',
    order_fee: '报单费用：',
    platform_bdfee: '平台手续费',
    cross_pool_equity: '穿越池投入：',
    bet_pool_equity: '对赌保证金：',
    optional: '可选',
    upgrade_receive: '升级后可接收：',
    second_generation_reward: '第{level}代推荐报单奖励',
    no_bet_task: '无对赌任务要求',
    activation_condition: '激活条件：',
    wallet_balance: '需链上钱包实际持仓 >20U的AILK (任意主流币)',
    activate_now: '立即激活',
    declaration_explanation: '报单说明',

    asset_overview: '我的资产总览',
    total_assets: '我的资产总额',
    ailk_balance: '余额',
    consumption_account: '消费账户',
    receive_payment: '收款',
    transfer_accounts: '转账',
    processing: '处理中...',
    recharge: '充值',
    withdrawal: '提现',
    referral_management: '推荐管理',
    referrer_count: '我的推荐人数：',
    bet_task_completed: '已完成对赌任务',
    sub_user_list: '我的下级列表',
    invitation_link: '我的邀请链接',
    lp_injection: '注入',
    equivalent: '等值',
    copy_invitation: '复制邀请链接',
    merchant_center: '商家中心',
    current_status: '当前状态:',
    activated: '已激活',
    notactive: '未激活',
    merchant_qualification: '商家资格条件进度:',
    prize_pool_participation: '奖池参股',
    merchant_earnings: '我的商家收益:',
    sub_merchant_list: '下级商家列表',
    activate_merchant: '点击激活',
    reward_records: '奖励记录',
    lottery_reward: '抽奖奖励记录',
    cross_pool_dividend: '穿越池分红记录',
    bet_pool_dividend: '对赌池分红记录',
    order_reward: '报单奖励记录',
    merchant_rights: '商家权益',
    WeightedaveragedailyoutputofAilkbasedontheshareofLPmining:'a、根据LP挖矿所占份额加权均分每日产出的Ailk',
    Subdepartmentminingrewards:'b、下级部门挖矿奖励',
    Storecustomerlotterywinnings1reward:'c、店铺客户抽奖中奖之后1%奖励',
    Submerchantcustomerwinnings1reward:'d、下级商家客户中奖1%收益的50%',

    only_transfer_assets_to_this_address: '仅向该地址转入BSC/BEP20相关资产',
    receiving_address: '收款地址',

    choose_wallet: '选择钱包',
    enter_or_paste_wallet_address: '输入或粘贴钱包地址',
    transfer_amount: '转账金额',
    please_enter_the_quantity: '请输入数量',
    all: '全部',
    wallet_zzbalance: '钱包余额',
    confirm: '确认',

    recharge_amount: '充值金额',
    please_enter_the_recharge_amount: '请输入充值金额',
    payment_required: '需支付',
    available: '可用',

    withdrawal_address: '提现地址',
    enter_or_long_press_to_paste_the_address: '输入或长按粘贴地址',
    quantity: '数量',
    minimum_withdrawal: '最少提现',
    a_starting_point: '个起',
    commission: '手续费',
    quantity_received: '到账数量',
    payment_password: '支付密码',
    please_enter_the_payment_password: '请输入支付密码',
    withdrawal_instructions: '提现说明',
    withdrawal_tip1: '1、24小时随时提现，无需审核，自动到账至绑定钱包（回链）',
    withdrawal_tip2: '2、提现手续费0.05%，但每次不少于0.1U的AILK（即手续费少于0.1U的Ailk时按0.1U的Ailk扣除）',

    mobile_wallet_tip: "请通过钱包APP访问",
    install_metamask: "请安装MetaMask或使用钱包APP",
    wallet_connect_failed: "钱包连接失败，请重试",
  }
} 