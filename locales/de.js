// de.js
export default {
  header: {
    'lang-title': 'Sprache auswählen',
    'switch-success': 'Zu {lang} gewechselt'
  },
  _Ailk: {
    thome: "Startseite",
    tlottery: "Lotterie",
    tdividend: "Dividenden",
    torder: "Bestellbereich",
    tmine: "<PERSON><PERSON>",

    welcome: 'Willkommen bei Ailk,',
    guide: 'Anleitung für Anfänger',
    quick_access: 'Schnellzugriff',
    lottery: 'Ziehung',
    dividend: 'Dividenden abholen',
    order: 'Bestellbereich',
    today_dynamic: 'Heutige Statistik',
    pool_total: 'Gesamtpool:',
    my_shares: 'Meine Aktien:',
    current_ratio: 'Aktie (aktueller Anteil',
    recommend_progress: 'Empfehlungsfortschritt:',
    target: 'Personen / Ziel',
    ren: "Person",
    remaining_time: 'Verbleibende Zeit:',
    beginner_guide: 'Anfängerhandbuch',
    loading: 'Lädt...',
    no_more_data: '<PERSON>ine weiteren Daten',

    consumption_amount: 'Konsumbetrag',
    discount_amount: 'Rabattbetrag',
    enter_consumption: 'Betrag eingeben',
    enter_discount: 'Rabat<PERSON> eingeben',
    first_prize: '1. Preis: 100% Erlass',
    second_prize: '2. Preis: 50% Rückerstattung',
    third_prize: '3. Preis: Zufälliger Betrag',
    draw: 'Ziehen',
    pool_status: 'Pool-Status',
    current_balance: 'Aktueller Saldo:',
    total_shares: 'Gesamtaktien:',
    strand: 'Aktie',
    per_share_value: 'Aktienwert:',
    invest: 'Investieren',
    withdraw: 'Entziehen',
    my_records: 'Meine Ziehungen',
    time: 'Zeit',
    result: 'Ergebnis',
    reward: 'Belohnung',

    my_total_dividend: 'Gesamtdividenden:',
    time_travel_betting: '(Zeitreise + Wette)',
    today_available: 'Heute verfügbar:',
    claim: 'Abholen',
    my_hshares: 'Meine Dividendenanteile',
    cross_pool: 'Cross-Pool',
    share: 'Anteil',
    bet_pool: 'Wett-Pool',
    notunlocked: 'Gesperrt',
    unlock: 'Freischalten',
    dividend_details: 'Dividendendetails',
    cross_pool_rule: 'Regeln: Tägliche Freigabe',
    bet_pool_rule: 'Regeln: Tägliche Teilung',
    cross_pool_investment: 'Investitionsbereich',
    current_zbalance: 'Aktueller Saldo:',
    my_investment: 'Meine Investition:',
    enter_amount: 'Betrag eingeben',
    tinvest: 'Investieren',
    platform_fee: '(3% Plattformgebühr)',
    claim_records: 'Abholhistorie',
    type: 'Typ',

    current_level: 'Mein Level:',
    referrer: 'Mein Werber:',
    bound: 'Gebunden',
    next_level: 'Level:',
    order_fee: 'Bestellgebühr:',
    platform_bdfee: 'Plattformgebühren',
    cross_pool_equity: 'Pool-Rechte:',
    bet_pool_equity: 'Wett-Pool-Rechte:',
    optional: 'Optional',
    upgrade_receive: 'Nach Upgrade erhältlich:',
    second_generation_reward: 'Generation {level} Empfehlungsbonus',
    no_bet_task: 'Keine Wettanforderung',
    activation_condition: 'Aktivierungsbedingung:',
    wallet_balance: 'Wallet >20U AILK (Hauptwährungen)',
    activate_now: 'Jetzt aktivieren',
    declaration_explanation: 'Bestellanleitung',

    asset_overview: 'Vermögensübersicht',
    total_assets: 'Gesamtvermögen',
    ailk_balance: 'Saldo',
    consumption_account: 'Verbrauchskonto',
    receive_payment: 'Zahlung erhalten',
    transfer_accounts: 'Überweisung',
    recharge: 'Aufladen',
    withdrawal: 'Auszahlung',
    referral_management: 'Empfehlungsverwaltung',
    referrer_count: 'Empfohlene Personen:',
    bet_task_completed: 'Wette abgeschlossen',
    sub_user_list: 'Untergeordnete Liste',
    invitation_link: 'Empfehlungslink',
    lp_injection: 'Einspritzung',
    equivalent: 'Äquivalent',
    copy_invitation: 'Link kopieren',
    merchant_center: 'Händlerzentrum',
    current_status: 'Status:',
    activated: 'Aktiviert',
    notactive: 'Nicht aktiv',
    merchant_qualification: 'Qualifikationsfortschritt:',
    prize_pool_participation: 'Pool-Beteiligung',
    merchant_earnings: 'Händlereinnahmen:',
    sub_merchant_list: 'Unterhändlerliste',
    activate_merchant: 'Aktivieren',
    reward_records: 'Belohnungshistorie',
    lottery_reward: 'Lotteriehistorie',
    cross_pool_dividend: 'Cross-Pool Dividenden',
    bet_pool_dividend: 'Wett-Pool Dividenden',
    order_reward: 'Bestellbelohnungen',
    merchant_rights: 'Händlerrechte',
    WeightedaveragedailyoutputofAilkbasedontheshareofLPmining:'a. Basierend auf der LP-Mining-Freigabe wird der tagesweise ausgegebene Ailk aufgewogen',
    Subdepartmentminingrewards:'b. Unterabteilungsminingbelohnungen',
    Storecustomerlotterywinnings1reward:'c. Store-Kundenziehungsgewinn 1% Belohnung',
    Submerchantcustomerwinnings1reward:'d. Unterhändlerkundenziehungsgewinn 1% Belohnung',


    only_transfer_assets_to_this_address: 'Nur BSC/BEP20-Assets',
    receiving_address: 'Empfangsadresse',

    choose_wallet: 'Wallet wählen',
    enter_or_paste_wallet_address: 'Adresse eingeben/einfügen',
    transfer_amount: 'Überweisungsbetrag',
    please_enter_the_quantity: 'Menge eingeben',
    all: 'Alle',
    wallet_zzbalance: 'Wallet-Saldo',
    confirm: 'Bestätigen',

    recharge_amount: 'Aufladebetrag',
    please_enter_the_recharge_amount: 'Betrag eingeben',
    payment_required: 'Zu zahlend',
    available: 'Verfügbar',

    withdrawal_address: 'Auszahlungsadresse',
    enter_or_long_press_to_paste_the_address: 'Eingabe oder langer Druck',
    quantity: 'Menge',
    minimum_withdrawal: 'Mindestauszahlung',
    a_starting_point: 'Minimum',
    commission: 'Gebühr',
    quantity_received: 'Erhaltene Menge',
    payment_password: 'Zahlungspasswort',
    please_enter_the_payment_password: 'Passwort eingeben',
    withdrawal_instructions: 'Auszahlungshinweise',
    withdrawal_tip1: '1. 24-Stunden-Auszahlungen sind jederzeit verfügbar, keine Bestätigung erforderlich, automatische Auszahlung an die Wallet.',
    withdrawal_tip2: '2. Bitte stellen Sie sicher, dass die Wallet-Adresse korrekt ist, andernfalls kann die Transaktion nicht rückgängig gemacht werden.',


    mobile_wallet_tip: "Bitte Wallet-App verwenden",
    install_metamask: "MetaMask installieren",
    wallet_connect_failed: "Verbindung fehlgeschlagen"
  }
}