// fr.js
export default {
  header: {
    'lang-title': 'Sélection de la langue',
    'switch-success': 'Passé à {lang}'
  },
  _Ailk: {
    thome: "Accueil",
    tlottery: "Tirage",
    tdividend: "Dividendes",
    torder: "Zone de déclaration",
    tmine: "Mon compte",

    welcome: 'Bienvenue sur Ailk,',
    guide: 'Voir le guide du débutant',
    quick_access: 'Accès rapide',
    lottery: 'Tirage',
    dividend: 'Recevoir des dividendes',
    order: 'Zone de commande',
    today_dynamic: 'Statistiques du jour',
    pool_total: 'Total du pot :',
    my_shares: 'Mes actions :',
    current_ratio: 'action (part actuelle',
    recommend_progress: 'Progrès recommandé :',
    target: 'personnes / objectif',
    ren: "personne",
    remaining_time: 'Temps restant :',
    beginner_guide: 'Guide du débutant',
    loading: 'Chargement...',
    no_more_data: 'Plus de données',

    consumption_amount: 'Montant dépensé',
    discount_amount: 'Montant remisé',
    enter_consumption: 'Entrez le montant',
    enter_discount: 'Entrez la remise',
    first_prize: '1er prix : exemption totale',
    second_prize: '2ème prix : remboursement 50%', 
    third_prize: '3ème prix : montant aléatoire',
    draw: 'Tirer',
    pool_status: 'État du pot',
    current_balance: 'Solde actuel :',
    total_shares: 'Actions totales :',
    strand: 'action',
    per_share_value: 'Valeur par action :',
    invest: 'Investir',
    withdraw: 'Retirer',
    my_records: 'Historique des tirages',
    time: 'Heure',
    result: 'Résultat',
    reward: 'Récompense',

    my_total_dividend: 'Dividendes totaux :',
    time_travel_betting: '(Voyage + Pari)',
    today_available: 'Disponible aujourd\'hui :',
    claim: 'Réclamer',
    my_hshares: 'Mes parts de dividendes',
    cross_pool: 'Pool transfrontalier',
    share: 'part',
    bet_pool: 'Pool de paris',
    notunlocked: 'Verrouillé',
    unlock: 'Déverrouiller',
    dividend_details: 'Détails des dividendes',
    cross_pool_rule: 'Règles : libération quotidienne',
    bet_pool_rule: 'Règles : partage quotidien',
    cross_pool_investment: 'Zone d\'investissement',
    current_zbalance: 'Solde actuel :',
    my_investment: 'Mon investissement :',
    enter_amount: 'Entrez le montant',
    tinvest: 'Investir',
    platform_fee: '(Frais de plateforme 3%)',
    claim_records: 'Historique des réclamations',
    type: 'Type',

    current_level: 'Mon niveau :',
    referrer: 'Mon parrain :',
    bound: 'Lié',
    next_level: 'Niveau :',
    order_fee: 'Frais de déclaration :',
    platform_bdfee: 'Frais de plateforme',
    cross_pool_equity: 'Droits du pool :',
    bet_pool_equity: 'Droits du pool de paris :',
    optional: 'Optionnel',
    upgrade_receive: 'À recevoir après mise à niveau :',
    second_generation_reward: 'Récompense parrainage {level}ème génération',
    no_bet_task: 'Aucun pari requis',
    activation_condition: 'Conditions d\'activation :',
    wallet_balance: 'Portefeuille >20U AILK (crypto-majeur)',
    activate_now: 'Activer maintenant',
    declaration_explanation: 'Explications',

    asset_overview: 'Vue d\'ensemble',
    total_assets: 'Actifs totaux',
    ailk_balance: 'Solde',
    consumption_account: 'Compte de dépenses',
    receive_payment: 'Recevoir',
    transfer_accounts: 'Virement',
    recharge: 'Recharger',
    withdrawal: 'Retrait',
    referral_management: 'Gestion parrainage',
    referrer_count: 'Filleuls :',
    bet_task_completed: 'Pari accompli',
    sub_user_list: 'Liste subordonnés',
    invitation_link: 'Lien de parrainage',
    lp_injection: 'Injection',
    equivalent: 'Équivalent',
    copy_invitation: 'Copier le lien',
    merchant_center: 'Centre marchand',
    current_status: 'Statut :',
    activated: 'Activé',
    notactive: 'Non activé',
    merchant_qualification: 'Progrès qualification :',
    prize_pool_participation: 'Participation au pot',
    merchant_earnings: 'Revenus marchands :',
    sub_merchant_list: 'Sous-marchands',
    activate_merchant: 'Activer',
    reward_records: 'Historique récompenses',
    lottery_reward: 'Historique tirages',
    cross_pool_dividend: 'Historique dividendes transfrontaliers',
    bet_pool_dividend: 'Historique dividendes paris',
    order_reward: 'Historique récompenses',
    merchant_rights: 'Droits marchands',
    WeightedaveragedailyoutputofAilkbasedontheshareofLPmining:'a. Ailk produit quotidien moyenné par le taux de part de LP',
    Subdepartmentminingrewards:'b. Rewards de mining des sous-départements',
    Storecustomerlotterywinnings1reward:'c. Gains de loterie des clients de magasins 1%',
    Submerchantcustomerwinnings1reward:'d. Gains de clients des sous-marchands 1%',

    only_transfer_assets_to_this_address: 'Uniquement BSC/BEP20',
    receiving_address: 'Adresse de réception',

    choose_wallet: 'Choisir un portefeuille',
    enter_or_paste_wallet_address: 'Entrez ou collez l\'adresse',
    transfer_amount: 'Montant du transfert',
    please_enter_the_quantity: 'Entrez la quantité',
    all: 'Tous',
    wallet_zzbalance: 'Solde du portefeuille',
    confirm: 'Confirmer',

    recharge_amount: 'Montant du recharge',
    please_enter_the_recharge_amount: 'Entrez le montant',
    payment_required: 'À payer',
    available: 'Disponible',

    withdrawal_address: 'Adresse de retrait',
    enter_or_long_press_to_paste_the_address: 'Entrez ou appuyez longuement',
    quantity: 'Quantité',
    minimum_withdrawal: 'Retrait minimum',
    a_starting_point: 'minimum',
    commission: 'Commission',
    quantity_received: 'Quantité reçue',
    payment_password: 'Mot de passe',
    please_enter_the_payment_password: 'Entrez le mot de passe',
    withdrawal_instructions: 'Instructions de retrait',
    withdrawal_tip1: '1. Retrait disponible 24h/24, sans confirmation, retrait automatique vers le portefeuille lié',
    withdrawal_tip2: '2. Le retrait est effectué en AILK, assurez-vous d\'avoir au moins 0,1 AILK pour couvrir les frais (frais de 0,05% appliqués, mais au moins 0,1 AILK est requis pour les frais inférieurs à 0,1 AILK)',

    mobile_wallet_tip: "Utilisez l'application portefeuille",
    install_metamask: "Installez MetaMask",
    wallet_connect_failed: "Échec de connexion"
  }
}