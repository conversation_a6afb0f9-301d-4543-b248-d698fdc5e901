export default {
  header: {
    'lang-title': 'Select Language',
    'switch-success': 'Switched to {lang}'
  },
  _Ailk: {
    thome: "Home",
    tlottery: "Lottery",
    tdividend: "Dividend",
    torder: "Orders",
    tmine: "Mine",

    welcome: 'Welcome to Ailk,',
    guide: 'Please check the beginner\'s guide for learning',
    quick_access: 'Quick Access',
    lottery: 'Lottery',
    dividend: 'Dividend Collection',
    order: 'Order Area',
    today_dynamic: 'Today\'s Activities',
    pool_total: 'Total Prize Pool: ',
    my_shares: 'My Shares: ',
    current_ratio: 'Shares (Current Proportion',
    recommend_progress: 'Referral Progress: ',
    target: 'People / Target',
    ren: 'People',
    remaining_time: 'Remaining Time: ',
    beginner_guide: 'Beginner\'s Guide',
    loading: 'Loading...',
    no_more_data: 'No more data',

    consumption_amount: 'Consumption Amount',
    discount_amount: 'Discount Amount',
    enter_consumption: 'Please enter consumption amount',
    enter_discount: 'Please enter discount amount',
    first_prize: 'First Prize = Full consumption waiver',
    second_prize: 'Second Prize = 50% discount refund',
    third_prize: 'Third Prize = Random amount',
    draw: 'Draw',
    pool_status: 'Current Pool Status',
    current_balance: 'Current Prize Pool Balance: ',
    total_shares: 'Total Pool Shares: ',
    strand: 'Stock',
    per_share_value: 'Value per Share: ',
    invest: 'Invest',
    withdraw: 'Withdraw',
    my_records: 'My Lottery Records',
    time: 'Time',
    result: 'Result',
    reward: 'Reward',

    my_total_dividend: 'My Total Dividends:',
    time_travel_betting: '(Time Travel + Betting)',
    today_available: 'Available for Claim Today:',
    claim: 'Claim',
    my_hshares: 'My Dividend Shares',
    cross_pool: 'Cross Pool',
    share: 'Share',
    bet_pool: 'Bet Pool',
    notunlocked: 'Not Unlocked',
    unlock: 'Unlock',
    dividend_details: 'Dividend Details',
    cross_pool_rule: 'Cross Pool Dividend Rule: 2% released daily',
    bet_pool_rule: 'Bet Pool Dividend Rule: 2% shared daily by task winners',
    cross_pool_investment: 'Cross Pool Investment Area',
    current_zbalance: 'Current Pool Balance:',
    my_investment: 'My Cross Investment:',
    enter_amount: 'Enter investment amount',
    tinvest: 'Invest',
    platform_fee: '(3% platform fee charged)',
    claim_records: 'Dividend Claim Records',
    time: 'Time',
    type: 'Type',
    reward: 'Reward',

    current_level: 'My Current Level: ',
    referrer: 'My Referrer: ',
    bound: '(Bound)',
    next_level: 'Level: ',
    order_fee: 'Order Fee:',
    platform_bdfee: 'Platform Fee',
    cross_pool_equity: 'Cross Pool Equity:',
    bet_pool_equity: 'Bet Pool Equity:',
    optional: 'Optional',
    upgrade_receive: 'After upgrade, you can receive:',
    second_generation_reward: '{level}nd Generation Referral Order Reward',
    no_bet_task: 'No Betting Tasks Required',
    activation_condition: 'Activation Condition:',
    wallet_balance: 'Requires actual on-chain wallet holdings of >20U AILK (any major cryptocurrency)',
    activate_now: 'Activate Now',
    declaration_explanation: 'Declaration Explanation',

    asset_overview: 'My Asset Overview',
    total_assets: 'Total Assets',
    ailk_balance: 'Ailk Balance',
    consumption_account: 'Consumption Account',
    receive_payment: 'Receive Payment',
    transfer_accounts: 'Transfer Accounts',
    recharge: 'Recharge',
    withdrawal: 'Withdrawal',
    referral_management: 'Referral Management',
    referrer_count: 'My Referral Count:',
    bet_task_completed: 'Bet Tasks Completed',
    sub_user_list: 'My Subordinate List',
    invitation_link: 'My Invitation Link',
    lp_injection: 'Injection',
    equivalent: 'Equivalent',
    copy_invitation: 'Copy Invitation Link',
    merchant_center: 'Merchant Center',
    current_status: 'Current Status:',
    activated: 'Activated',
    notactive: 'Not Activated',
    merchant_qualification: 'Merchant Qualification Progress:',
    prize_pool_participation: 'Prize Pool Participation',
    merchant_earnings: 'My Merchant Earnings:',
    sub_merchant_list: 'Sub-Merchant List',
    activate_merchant: 'Activate Now',
    reward_records: 'Reward Records',
    lottery_reward: 'Lottery Reward Records',
    cross_pool_dividend: 'Cross Pool Dividend Records',
    bet_pool_dividend: 'Bet Pool Dividend Records',
    order_reward: 'Order Reward Records',
    merchant_rights: 'Merchant Rights',
    WeightedaveragedailyoutputofAilkbasedontheshareofLPmining: 'a. Daily distribution of Ailk based on LP mining share',
    Subdepartmentminingrewards: 'b. Sub-department mining rewards',
    Storecustomerlotterywinnings1reward: 'c. 1% reward for store customer lottery winnings',
    Submerchantcustomerwinnings1reward: 'd. 50% of 1% earnings from sub-merchant customer winnings',

    only_transfer_assets_to_this_address: 'Only transfer BSC/BEP20 related assets to this address',
    receiving_address: 'Receiving Address',

    choose_wallet: 'Choose Wallet',
    enter_or_paste_wallet_address: 'Enter or Paste Wallet Address',
    transfer_amount: 'Transfer Amount',
    please_enter_the_quantity: 'Please enter quantity',
    all: 'All',
    wallet_zzbalance: 'Wallet Balance',
    confirm: 'Confirm',

    recharge_amount: 'Recharge Amount',
    please_enter_the_recharge_amount: 'Please enter recharge amount',
    payment_required: 'Payment Required',
    available: 'Available',

    withdrawal_address: 'Withdrawal Address',
    enter_or_long_press_to_paste_the_address: 'Enter or long press to paste address',
    quantity: 'Quantity',
    minimum_withdrawal: 'Minimum Withdrawal',
    a_starting_point: 'A starting point',
    commission: 'Commission',
    quantity_received: 'Quantity received',
    payment_password: 'Payment Password',
    please_enter_the_payment_password: 'Please enter payment password',
    withdrawal_instructions: 'Withdrawal Instructions',
    withdrawal_tip1: '1. Withdraw anytime, 24/7, no confirmation required, automatically transferred to bound wallet (return link)',
    withdrawal_tip2: '2. Minimum withdrawal amount is 1U, and the commission is 0.05% but not less than 0.1U AILK (if the commission is less than 0.1U AILK, it will deduct 0.1U AILK)',

    "mobile_wallet_tip": "Please access via wallet app",
    "install_metamask": "Please install MetaMask or use wallet app",
    "wallet_connect_failed": "Wallet connection failed, please try again",
  }
} 