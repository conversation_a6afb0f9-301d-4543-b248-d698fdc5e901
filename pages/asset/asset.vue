<template>
  <view class="container">
<!-- 顶部导航栏 -->
		<Header :on-login="dengdai"></Header>
    <!-- 资产总览 -->
    <view class="section-title">资产</view>
    <view class="power-card">
      <view class="power-header">
        <text class="power-title">NFT算力</text>
        <image class="nft-icon" src="/static/nft.png"></image>
      </view>
      <view class="power-content">
        <view class="power-item">
          <text class="power-value">0.00</text>
          <text class="power-label">我的NFT算力</text>
        </view>
        <view class="power-item">
          <text class="power-value">0.00</text>
          <text class="power-label">小社区NFT算力</text>
        </view>
      </view>
    </view>

    <!-- NFT兑换 -->
    <view class="section-title">NFT兑换</view>
    <scroll-view
      class="nft-scroll-container"
      scroll-x="true"
      show-scrollbar="false"
      enable-flex="true"
      scroll-with-animation="true"
    >
      <view class="nft-list">
        <view class="nft-card" v-for="item in nftList" :key="item.id">
          <view class="nft-img-container">
            <image class="nft-img" :src="item.img"></image>
            <view class="nft-glow"></view>
          </view>
          <text class="nft-title">{{item.title}}</text>
          <text class="nft-rate">{{item.rate}}</text>
          <button class="nft-btn">{{item.price}}</button>
        </view>
      </view>
    </scroll-view>

    <!-- 可领取资产 -->
    <view class="asset-row">
      <text class="asset-title">可领取资产</text>
      <view class="asset-detail" @click="goDetail">
        <text class="detail-text">资产明细</text>
        <text class="arrow">></text>
      </view>
    </view>
    <view class="claim-card">
      <view class="claim-item">
        <view class="claim-info">
          <text class="claim-value">0</text>
          <text class="claim-label">静态收益</text>
        </view>
        <button class="claim-btn">立即提取</button>
      </view>
      <view class="claim-item">
        <view class="claim-info">
          <text class="claim-value">0</text>
          <text class="claim-label">动态收益</text>
        </view>
        <button class="claim-btn">立即提取</button>
      </view>
    </view>
  </view>
</template>

<script>
import Header from '/components/common/header.vue';
export default {
  components: {
		Header
	},
  data() {
    return {
      nftList: [
        { id: 1, img: '/static/nft1.png', title: '100USDT萌芽之旅', rate: '1.02倍算力', price: '100 USDT' },
        { id: 2, img: '/static/nft1.png', title: '100USDT萌芽之旅', rate: '1.02倍算力', price: '100 USDT' },
        { id: 3, img: '/static/nft1.png', title: '100USDT萌芽之旅', rate: '1.02倍算力', price: '100 USDT' },
        { id: 4, img: '/static/nft1.png', title: '500USDT成长之旅', rate: '1.05倍算力', price: '500 USDT' },
        { id: 5, img: '/static/nft1.png', title: '1000USDT进阶之旅', rate: '1.08倍算力', price: '1000 USDT' },
        { id: 6, img: '/static/nft1.png', title: '2000USDT专家之旅', rate: '1.12倍算力', price: '2000 USDT' }
      ]
    }
  },
  methods: {
    goDetail() {
      uni.navigateTo({ url: '/pages/assetDetail/assetDetail' })
    }
  }
}
</script>

<style scoped>
.container {
  background: #000000;
  min-height: 100vh;
  padding: 0;
  color: #fff;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 状态栏 */
.status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 40rpx 10rpx 40rpx;
  background: #000;
}

.time {
  color: #fff;
  font-size: 32rpx;
  font-weight: 600;
}

.status-icons {
  display: flex;
  align-items: center;
  gap: 10rpx;
}

.signal-bars {
  display: flex;
  align-items: flex-end;
  gap: 4rpx;
}

.bar {
  width: 6rpx;
  background: #fff;
  border-radius: 2rpx;
}

.bar:nth-child(1) { height: 8rpx; }
.bar:nth-child(2) { height: 12rpx; }
.bar:nth-child(3) { height: 16rpx; }
.bar:nth-child(4) { height: 20rpx; }

.wifi-icon, .battery {
  color: #fff;
  font-size: 28rpx;
}

/* 顶部导航 */
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 40rpx;
}

.logo-container {
  display: flex;
  align-items: center;
}

.logo {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  margin-right: 16rpx;
}

.logo-text {
  color: #fff;
  font-size: 28rpx;
  font-weight: 500;
}

.wallet {
  display: flex;
  align-items: center;
  background: #333333;
  border-radius: 30rpx;
  padding: 16rpx 24rpx;
  position: relative;
}

.wallet::before {
  content: '💎';
  position: absolute;
  left: 8rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 24rpx;
}

.wallet-icon {
  width: 20rpx;
  height: 20rpx;
  margin-right: 12rpx;
  margin-left: 20rpx;
}

.wallet-text {
  color: #fff;
  font-size: 24rpx;
  font-weight: 400;
}

.asset-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #fff;
  font-size: 22rpx;
  font-weight: 400;
}

.asset-icon {
  width: 44rpx;
  height: 44rpx;
  margin-bottom: 8rpx;
}

/* 资产标题 */
.section-title {
  color: #fff;
  font-size: 32rpx;
  margin: 48rpx 20rpx 44rpx 32rpx;
  font-weight: 500;
}

/* NFT算力卡片 */
.power-card {
  background: #1a1a1a;
  border-radius: 32rpx;
  margin: 0 40rpx 40rpx 40rpx;
  overflow: hidden;
}

.power-header {
  background: #00EAFF;
  padding: 32rpx 40rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.power-title {
  color: #000;
  font-size: 32rpx;
  font-weight: 600;
}

.nft-icon {
  width: 60rpx;
  height: 60rpx;
}

.power-content {
  display: flex;
  justify-content: space-around;
  padding: 38rpx 40rpx 50rpx 40rpx;
}

.power-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.power-value {
  color: #fff;
  font-size: 48rpx;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.power-label {
  color: #999999;
  font-size: 28rpx;
  font-weight: 400;
}

/* NFT兑换列表 */
.nft-scroll-container {
  width: 100%;
  white-space: nowrap;
  padding-bottom: 40rpx;
}

.nft-list {
  display: flex;
  flex-direction: row;
  padding: 0 40rpx;
  gap: 30rpx;
  width: max-content;
}

.nft-card {
  background: #1a1a1a;
  border-radius: 24rpx;
  width: 280rpx;
  padding: 30rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  flex-shrink: 0;
  box-sizing: border-box;
}

.nft-img-container {
  position: relative;
  margin-bottom: 24rpx;
}

.nft-img {
  width: 160rpx;
  height: 160rpx;
  border-radius: 50%;
  position: relative;
  z-index: 2;
}

.nft-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 200rpx;
  height: 200rpx;
  background: radial-gradient(circle, rgba(0, 234, 255, 0.3) 0%, rgba(0, 234, 255, 0.1) 50%, transparent 70%);
  border-radius: 50%;
  z-index: 1;
}

.nft-title {
  color: #fff;
  font-size: 28rpx;
  font-weight: 500;
  text-align: center;
  margin-bottom: 12rpx;
}

.nft-rate {
  color: #02F6F6;
  font-size: 24rpx;
  font-weight: 500;
  margin-bottom: 24rpx;
}

.nft-btn {
  background: #02F6F6;
  color: #000;
  border-radius: 30rpx;
  width: 200rpx;
  height: 60rpx;
  font-size: 28rpx;
  font-weight: 600;
  border: none;
}

/* 可领取资产 */
.asset-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 40rpx;
  margin-bottom: 30rpx;
}

.asset-title {
  color: #fff;
  font-size: 36rpx;
  font-weight: 600;
  ma
}

.asset-detail {
  display: flex;
  align-items: center;
  color: #999999;
  font-size: 28rpx;
  font-weight: 400;
}

.detail-text {
  margin-right: 8rpx;
}

.arrow {
  color: #999999;
  font-size: 28rpx;
}

.claim-card {
  background: #1a1a1a;
  border-radius: 24rpx;
  padding: 40rpx;
  margin: 0 40rpx 40rpx 40rpx;
}

.claim-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 50rpx;
}

.claim-item:last-child {
  margin-bottom: 0;
}

.claim-info {
  display: flex;
  flex-direction: column;
}

.claim-value {
  color: #fff;
  font-size: 56rpx;
  font-weight: 700;
  margin-bottom: 8rpx;
}

.claim-label {
  color: #999999;
  font-size: 28rpx;
  font-weight: 400;
}

.claim-btn {
  background: #00EAFF;
  color: #000;
  border-radius: 30rpx;
  width: 200rpx;
  height: 70rpx;
  font-size: 30rpx;
  font-weight: 600;
  border: none;
}
</style>