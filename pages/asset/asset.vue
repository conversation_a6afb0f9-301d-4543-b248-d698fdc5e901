<template>
  <view class="container">
    <!-- 顶部栏 -->
    <view class="header">
      <image class="logo" src="/static/logo.png"></image>
      <view class="wallet">
        <image class="wallet-icon" src="/static/binance.png"></image>
        <text class="wallet-text">0×2e4d...33cd49</text>
      </view>
      <view class="asset-btn">
        <image class="asset-icon" src="/static/asset.png"></image>
        <text>资产</text>
      </view>
    </view>

    <!-- 资产总览 -->
    <view class="section-title">资产</view>
    <view class="power-card">
      <view class="power-header">
        <text>NFT算力</text>
        <image class="nft-icon" src="/static/nft.png"></image>
      </view>
      <view class="power-content">
        <view class="power-item">
          <text class="power-value">0.00</text>
          <text class="power-label">我的NFT算力</text>
        </view>
        <view class="power-item">
          <text class="power-value">0.00</text>
          <text class="power-label">小社区NFT算力</text>
        </view>
      </view>
    </view>

    <!-- NFT兑换 -->
    <view class="section-title">NFT兑换</view>
    <scroll-view class="nft-list" scroll-x>
      <view class="nft-card" v-for="item in nftList" :key="item.id">
        <image class="nft-img" :src="item.img"></image>
        <text class="nft-title">{{item.title}}</text>
        <text class="nft-rate">{{item.rate}}</text>
        <button class="nft-btn">{{item.price}}</button>
      </view>
    </scroll-view>

    <!-- 可领取资产 -->
    <view class="asset-row">
      <text>可领取资产</text>
      <view class="asset-detail" @click="goDetail">
        <text>资产明细</text>
        <uni-icons type="right" size="18" color="#fff"/>
      </view>
    </view>
    <view class="claim-card">
      <view class="claim-item">
        <view>
          <text class="claim-value">0</text>
          <text class="claim-label">静态收益</text>
        </view>
        <button class="claim-btn">立即提取</button>
      </view>
      <view class="claim-item">
        <view>
          <text class="claim-value">0</text>
          <text class="claim-label">动态收益</text>
        </view>
        <button class="claim-btn">立即提取</button>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      nftList: [
        { id: 1, img: '/static/nft1.png', title: '100USDT萌芽之旅', rate: '1.02倍算力', price: '100 USDT' },
        { id: 2, img: '/static/nft1.png', title: '100USDT萌芽之旅', rate: '1.02倍算力', price: '100 USDT' },
        { id: 3, img: '/static/nft1.png', title: '100USDT萌芽之旅', rate: '1.02倍算力', price: '100 USDT' }
      ]
    }
  },
  methods: {
    goDetail() {
      uni.navigateTo({ url: '/pages/assetDetail/assetDetail' })
    }
  }
}
</script>

<style scoped>
.container {
  background: #181818;
  min-height: 100vh;
  padding: 0 20rpx;
}
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: 60rpx;
}
.logo { width: 80rpx; height: 80rpx; }
.wallet {
  display: flex;
  align-items: center;
  background: #232323;
  border-radius: 20rpx;
  padding: 10rpx 20rpx;
}
.wallet-icon { width: 32rpx; height: 32rpx; margin-right: 10rpx; }
.wallet-text { color: #fff; font-size: 24rpx; }
.asset-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #fff;
  font-size: 22rpx;
}
.asset-icon { width: 40rpx; height: 40rpx; }
.section-title {
  color: #fff;
  font-size: 36rpx;
  margin: 40rpx 0 20rpx 0;
  font-weight: bold;
}
.power-card {
  background: #232323;
  border-radius: 40rpx;
  padding: 0 0 40rpx 0;
  margin-bottom: 40rpx;
}
.power-header {
  background: #00eaff;
  border-radius: 40rpx 40rpx 0 0;
  padding: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.nft-icon { width: 60rpx; height: 60rpx; }
.power-content {
  display: flex;
  justify-content: space-around;
  margin-top: 40rpx;
}
.power-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.power-value {
  color: #fff;
  font-size: 48rpx;
  font-weight: bold;
}
.power-label {
  color: #bdbdbd;
  font-size: 24rpx;
  margin-top: 10rpx;
}
.nft-list {
  display: flex;
  flex-direction: row;
  margin-bottom: 40rpx;
}
.nft-card {
  background: #232323;
  border-radius: 30rpx;
  margin-right: 20rpx;
  width: 300rpx;
  padding: 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.nft-img { width: 200rpx; height: 200rpx; border-radius: 20rpx; }
.nft-title { color: #fff; font-size: 28rpx; margin-top: 20rpx; }
.nft-rate { color: #00eaff; font-size: 22rpx; margin: 10rpx 0; }
.nft-btn {
  background: #00eaff;
  color: #181818;
  border-radius: 30rpx;
  width: 200rpx;
  margin-top: 10rpx;
}
.asset-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #fff;
  font-size: 32rpx;
  margin-bottom: 20rpx;
}
.asset-detail {
  display: flex;
  align-items: center;
  color: #bdbdbd;
  font-size: 26rpx;
}
.claim-card {
  background: #232323;
  border-radius: 30rpx;
  padding: 30rpx 20rpx;
  margin-bottom: 40rpx;
}
.claim-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}
.claim-value {
  color: #fff;
  font-size: 40rpx;
  font-weight: bold;
}
.claim-label {
  color: #bdbdbd;
  font-size: 24rpx;
  margin-left: 10rpx;
}
.claim-btn {
  background: #00eaff;
  color: #181818;
  border-radius: 30rpx;
  width: 160rpx;
}
</style>