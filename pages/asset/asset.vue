<template>
  <view class="container">
    <!-- 顶部栏 -->
    <view class="header">
      <image class="logo" src="/static/logo.png"></image>
      <view class="wallet">
        <image class="wallet-icon" src="/static/binance.png"></image>
        <text class="wallet-text">0×2e4d...33cd49</text>
      </view>
      <view class="asset-btn">
        <image class="asset-icon" src="/static/asset.png"></image>
        <text>资产</text>
      </view>
    </view>

    <!-- 资产总览 -->
    <view class="section-title">资产</view>
    <view class="power-card">
      <view class="power-header">
        <text>NFT算力</text>
        <image class="nft-icon" src="/static/nft.png"></image>
      </view>
      <view class="power-content">
        <view class="power-item">
          <text class="power-value">0.00</text>
          <text class="power-label">我的NFT算力</text>
        </view>
        <view class="power-item">
          <text class="power-value">0.00</text>
          <text class="power-label">小社区NFT算力</text>
        </view>
      </view>
    </view>

    <!-- NFT兑换 -->
    <view class="section-title">NFT兑换</view>
    <scroll-view class="nft-list" scroll-x>
      <view class="nft-card" v-for="item in nftList" :key="item.id">
        <image class="nft-img" :src="item.img"></image>
        <text class="nft-title">{{item.title}}</text>
        <text class="nft-rate">{{item.rate}}</text>
        <button class="nft-btn">{{item.price}}</button>
      </view>
    </scroll-view>

    <!-- 可领取资产 -->
    <view class="asset-row">
      <text>可领取资产</text>
      <view class="asset-detail" @click="goDetail">
        <text>资产明细</text>
        <uni-icons type="right" size="18" color="#fff"/>
      </view>
    </view>
    <view class="claim-card">
      <view class="claim-item">
        <view>
          <text class="claim-value">0</text>
          <text class="claim-label">静态收益</text>
        </view>
        <button class="claim-btn">立即提取</button>
      </view>
      <view class="claim-item">
        <view>
          <text class="claim-value">0</text>
          <text class="claim-label">动态收益</text>
        </view>
        <button class="claim-btn">立即提取</button>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      nftList: [
        { id: 1, img: '/static/nft1.png', title: '100USDT萌芽之旅', rate: '1.02倍算力', price: '100 USDT' },
        { id: 2, img: '/static/nft1.png', title: '100USDT萌芽之旅', rate: '1.02倍算力', price: '100 USDT' },
        { id: 3, img: '/static/nft1.png', title: '100USDT萌芽之旅', rate: '1.02倍算力', price: '100 USDT' }
      ]
    }
  },
  methods: {
    goDetail() {
      uni.navigateTo({ url: '/pages/assetDetail/assetDetail' })
    }
  }
}
</script>

<style scoped>
.container {
  background: #0a0a0a;
  min-height: 100vh;
  padding: 0 30rpx;
  color: #fff;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 60rpx 0 40rpx 0;
  position: relative;
}

.logo {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
}

.wallet {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 25rpx;
  padding: 12rpx 20rpx;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.wallet-icon {
  width: 24rpx;
  height: 24rpx;
  margin-right: 12rpx;
}

.wallet-text {
  color: #fff;
  font-size: 26rpx;
  font-weight: 500;
}

.asset-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #fff;
  font-size: 24rpx;
  font-weight: 500;
}

.asset-icon {
  width: 48rpx;
  height: 48rpx;
  margin-bottom: 8rpx;
}

.section-title {
  color: #fff;
  font-size: 40rpx;
  margin: 0 0 30rpx 0;
  font-weight: 600;
}

.power-card {
  background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%);
  border-radius: 24rpx;
  padding: 0;
  margin-bottom: 50rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.power-header {
  background: linear-gradient(135deg, #00d4ff 0%, #00b8e6 100%);
  border-radius: 24rpx 24rpx 0 0;
  padding: 24rpx 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.power-header text {
  color: #000;
  font-size: 32rpx;
  font-weight: 600;
}

.nft-icon {
  width: 64rpx;
  height: 64rpx;
}

.power-content {
  display: flex;
  justify-content: space-around;
  padding: 50rpx 30rpx 40rpx 30rpx;
}

.power-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.power-value {
  color: #fff;
  font-size: 56rpx;
  font-weight: 700;
  margin-bottom: 12rpx;
}

.power-label {
  color: #999;
  font-size: 26rpx;
  font-weight: 400;
}

.nft-list {
  display: flex;
  flex-direction: row;
  margin-bottom: 50rpx;
  padding-bottom: 20rpx;
}

.nft-card {
  background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%);
  border-radius: 20rpx;
  margin-right: 24rpx;
  width: 280rpx;
  padding: 24rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.2);
}

.nft-img {
  width: 180rpx;
  height: 180rpx;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
}

.nft-title {
  color: #fff;
  font-size: 26rpx;
  font-weight: 500;
  text-align: center;
  margin-bottom: 12rpx;
}

.nft-rate {
  color: #00d4ff;
  font-size: 24rpx;
  font-weight: 500;
  margin-bottom: 20rpx;
}

.nft-btn {
  background: linear-gradient(135deg, #00d4ff 0%, #00b8e6 100%);
  color: #000;
  border-radius: 25rpx;
  width: 180rpx;
  height: 50rpx;
  font-size: 26rpx;
  font-weight: 600;
  border: none;
  box-shadow: 0 4rpx 12rpx rgba(0, 212, 255, 0.3);
}

.asset-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #fff;
  font-size: 36rpx;
  font-weight: 600;
  margin-bottom: 30rpx;
}

.asset-detail {
  display: flex;
  align-items: center;
  color: #999;
  font-size: 28rpx;
  font-weight: 400;
}

.claim-card {
  background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%);
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  margin-bottom: 40rpx;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.3);
}

.claim-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40rpx;
}

.claim-item:last-child {
  margin-bottom: 0;
}

.claim-value {
  color: #fff;
  font-size: 48rpx;
  font-weight: 700;
  margin-bottom: 8rpx;
}

.claim-label {
  color: #999;
  font-size: 26rpx;
  font-weight: 400;
}

.claim-btn {
  background: linear-gradient(135deg, #00d4ff 0%, #00b8e6 100%);
  color: #000;
  border-radius: 25rpx;
  width: 180rpx;
  height: 60rpx;
  font-size: 28rpx;
  font-weight: 600;
  border: none;
  box-shadow: 0 4rpx 12rpx rgba(0, 212, 255, 0.3);
}
</style>