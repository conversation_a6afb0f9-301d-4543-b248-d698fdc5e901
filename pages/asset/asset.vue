<template>
  <view class="container">
<!-- 顶部导航栏 -->
		<Header :on-login="dengdai"></Header>
    <!-- 资产总览 -->
    <view class="section-title">资产</view>
    <view class="power-card">
      <view class="power-header">
        <text class="power-title">NFT算力</text>
        <image class="nft-icon" src="/static/nft.png"></image>
      </view>
      <view class="power-content">
        <view class="power-item">
          <text class="power-value">0.00</text>
          <text class="power-label">我的NFT算力</text>
        </view>
        <view class="power-item">
          <text class="power-value">0.00</text>
          <text class="power-label">小社区NFT算力</text>
        </view>
      </view>
    </view>

    <!-- NFT兑换 -->
    <view class="section-title">NFT兑换</view>
    <scroll-view
      class="nft-scroll-container"
      scroll-x="true"
      show-scrollbar="false"
      enable-flex="true"
      scroll-with-animation="true"
    >
      <view class="nft-list">
        <view class="nft-card" v-for="item in nftList" :key="item.id">
          <view class="nft-img-container">
            <image class="nft-img" :src="item.img"></image>
            <view class="nft-glow"></view>
          </view>
          <text class="nft-title">{{item.title}}</text>
          <text class="nft-rate">{{item.rate}}</text>
          <button class="nft-btn" @click="showPurchaseModal(item)">{{item.price}}</button>
        </view>
      </view>
    </scroll-view>

    <!-- 可领取资产 -->
    <view class="asset-row">
      <text class="asset-title">可领取资产</text>
      <view class="asset-detail" @click="goDetail">
        <text class="detail-text">资产明细</text>
        <text class="arrow">></text>
      </view>
    </view>
    <view class="claim-card">
      <view class="claim-item">
        <view class="claim-info">
          <text class="claim-value">0</text>
          <text class="claim-label">静态收益</text>
        </view>
        <button class="claim-btn">立即提取</button>
      </view>
      <view class="claim-item">
        <view class="claim-info">
          <text class="claim-value">0</text>
          <text class="claim-label">动态收益</text>
        </view>
        <button class="claim-btn">立即提取</button>
      </view>
    </view>

    <!-- NFT购买弹窗 -->
    <view class="modal-overlay" v-if="showModal" @click="closeModal">
      <view class="modal-content" @click.stop>
        <view class="modal-header">
          <text class="modal-title">{{selectedNft.title}}</text>
        </view>

        <view class="modal-body">
          <view class="payment-section">
            <text class="section-label">需支付</text>
            <view class="payment-input">
              <input class="amount-input" v-model="paymentAmount" type="number" />
              <text class="currency">USDT</text>
            </view>
          </view>

          <view class="add-section">
            <text class="add-icon">+</text>
          </view>

          <view class="balance-section">
            <input class="balance-input" v-model="balanceAmount" type="number" />
            <text class="infinity">∞</text>
          </view>

          <view class="info-section">
            <text class="usdt-balance">USDT余额: <text class="balance-value">17.232</text></text>
          </view>

          <view class="power-section">
            <text class="power-title">获得算力</text>
            <view class="power-row">
              <view class="power-item">
                <text class="power-label">基础算力倍数: <text class="power-value">{{selectedNft.baseRate}}</text></text>
                <text class="power-label">算力外倍: <text class="power-value">{{selectedNft.extraRate}}</text></text>
              </view>
              <view class="power-item">
                <text class="power-label">基础算力: <text class="power-value">{{selectedNft.basePower}}</text></text>
                <text class="power-label">可获得算力: <text class="power-value">{{selectedNft.totalPower}}</text></text>
              </view>
            </view>
          </view>

          <button class="confirm-btn" @click="confirmPurchase">确认购买</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import Header from '/components/common/header.vue';
export default {
  components: {
		Header
	},
  data() {
    return {
      showModal: false,
      selectedNft: {},
      paymentAmount: 100,
      balanceAmount: 0,
      nftList: [
        {
          id: 1,
          img: '/static/nft1.png',
          title: '100USDT萌芽之旅',
          rate: '1.02倍算力',
          price: '100 USDT',
          baseRate: '1.02倍',
          extraRate: '1.02*17',
          basePower: '102',
          totalPower: '142.2321'
        },
        {
          id: 2,
          img: '/static/nft1.png',
          title: '100USDT萌芽之旅',
          rate: '1.02倍算力',
          price: '100 USDT',
          baseRate: '1.02倍',
          extraRate: '1.02*17',
          basePower: '102',
          totalPower: '142.2321'
        },
        {
          id: 3,
          img: '/static/nft1.png',
          title: '100USDT萌芽之旅',
          rate: '1.02倍算力',
          price: '100 USDT',
          baseRate: '1.02倍',
          extraRate: '1.02*17',
          basePower: '102',
          totalPower: '142.2321'
        },
        {
          id: 4,
          img: '/static/nft1.png',
          title: '500USDT成长之旅',
          rate: '1.05倍算力',
          price: '500 USDT',
          baseRate: '1.05倍',
          extraRate: '1.05*17',
          basePower: '525',
          totalPower: '721.25'
        },
        {
          id: 5,
          img: '/static/nft1.png',
          title: '1000USDT进阶之旅',
          rate: '1.08倍算力',
          price: '1000 USDT',
          baseRate: '1.08倍',
          extraRate: '1.08*17',
          basePower: '1080',
          totalPower: '1512.6'
        },
        {
          id: 6,
          img: '/static/nft1.png',
          title: '2000USDT专家之旅',
          rate: '1.12倍算力',
          price: '2000 USDT',
          baseRate: '1.12倍',
          extraRate: '1.12*17',
          basePower: '2240',
          totalPower: '3164.8'
        }
      ]
    }
  },
  methods: {
    goDetail() {
      uni.navigateTo({ url: '/pages/assetDetail/assetDetail' })
    },
    showPurchaseModal(item) {
      this.selectedNft = item
      this.paymentAmount = parseInt(item.price.replace(' USDT', ''))
      this.balanceAmount = 0
      this.showModal = true
    },
    closeModal() {
      this.showModal = false
    },
    confirmPurchase() {
      // 这里添加购买逻辑
      uni.showToast({
        title: '购买成功',
        icon: 'success'
      })
      this.closeModal()
    }
  }
}
</script>

<style scoped>
.container {
  background: #000000;
  min-height: 100vh;
  padding: 0;
  color: #fff;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 状态栏 */
.status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 40rpx 10rpx 40rpx;
  background: #000;
}

.time {
  color: #fff;
  font-size: 32rpx;
  font-weight: 600;
}

.status-icons {
  display: flex;
  align-items: center;
  gap: 10rpx;
}

.signal-bars {
  display: flex;
  align-items: flex-end;
  gap: 4rpx;
}

.bar {
  width: 6rpx;
  background: #fff;
  border-radius: 2rpx;
}

.bar:nth-child(1) { height: 8rpx; }
.bar:nth-child(2) { height: 12rpx; }
.bar:nth-child(3) { height: 16rpx; }
.bar:nth-child(4) { height: 20rpx; }

.wifi-icon, .battery {
  color: #fff;
  font-size: 28rpx;
}

/* 顶部导航 */
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 40rpx;
}

.logo-container {
  display: flex;
  align-items: center;
}

.logo {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  margin-right: 16rpx;
}

.logo-text {
  color: #fff;
  font-size: 28rpx;
  font-weight: 500;
}

.wallet {
  display: flex;
  align-items: center;
  background: #333333;
  border-radius: 30rpx;
  padding: 16rpx 24rpx;
  position: relative;
}

.wallet::before {
  content: '💎';
  position: absolute;
  left: 8rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 24rpx;
}

.wallet-icon {
  width: 20rpx;
  height: 20rpx;
  margin-right: 12rpx;
  margin-left: 20rpx;
}

.wallet-text {
  color: #fff;
  font-size: 24rpx;
  font-weight: 400;
}

.asset-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #fff;
  font-size: 22rpx;
  font-weight: 400;
}

.asset-icon {
  width: 44rpx;
  height: 44rpx;
  margin-bottom: 8rpx;
}

/* 资产标题 */
.section-title {
  color: #fff;
  font-size: 32rpx;
  margin: 48rpx 20rpx 44rpx 32rpx;
  font-weight: 500;
}

/* NFT算力卡片 */
.power-card {
  background: #1a1a1a;
  border-radius: 32rpx;
  margin: 0 40rpx 40rpx 40rpx;
  overflow: hidden;
}

.power-header {
  background: #00EAFF;
  padding: 32rpx 40rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.power-title {
  color: #000;
  font-size: 32rpx;
  font-weight: 600;
}

.nft-icon {
  width: 60rpx;
  height: 60rpx;
}

.power-content {
  display: flex;
  justify-content: space-around;
  padding: 38rpx 40rpx 50rpx 40rpx;
}

.power-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.power-value {
  color: #fff;
  font-size: 48rpx;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.power-label {
  color: #999999;
  font-size: 28rpx;
  font-weight: 400;
}

/* NFT兑换列表 */
.nft-scroll-container {
  width: 100%;
  white-space: nowrap;
  padding-bottom: 40rpx;
}

.nft-list {
  display: flex;
  flex-direction: row;
  padding: 0 40rpx;
  gap: 30rpx;
  width: max-content;
}

.nft-card {
  background: #1a1a1a;
  border-radius: 24rpx;
  width: 280rpx;
  padding: 30rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  flex-shrink: 0;
  box-sizing: border-box;
}

.nft-img-container {
  position: relative;
  margin-bottom: 24rpx;
}

.nft-img {
  width: 160rpx;
  height: 160rpx;
  border-radius: 50%;
  position: relative;
  z-index: 2;
}

.nft-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 200rpx;
  height: 200rpx;
  background: radial-gradient(circle, rgba(0, 234, 255, 0.3) 0%, rgba(0, 234, 255, 0.1) 50%, transparent 70%);
  border-radius: 50%;
  z-index: 1;
}

.nft-title {
  color: #fff;
  font-size: 28rpx;
  font-weight: 500;
  text-align: center;
  margin-bottom: 12rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
  max-width: 200rpx;
}

.nft-rate {
  color: #02F6F6;
  font-size: 24rpx;
  font-weight: 500;
  margin-bottom: 24rpx;
}

.nft-btn {
  background: #02F6F6;
  color: #000;
  border-radius: 30rpx;
  width: 200rpx;
  height: 60rpx;
  font-size: 28rpx;
  font-weight: 600;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  line-height: 1;
}

/* 可领取资产 */
.asset-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 40rpx;
  margin-bottom: 36rpx;
}

.asset-title {
  color: #fff;
  font-size: 36rpx;
  font-weight: 600;
}

.asset-detail {
  display: flex;
  align-items: center;
  color: #999999;
  font-size: 28rpx;
  font-weight: 400;
}

.detail-text {
  margin-right: 8rpx;
}

.arrow {
  color: #999999;
  font-size: 28rpx;
}

.claim-card {
  background: #1a1a1a;
  border-radius: 24rpx;
  padding: 40rpx;
  margin: 0 40rpx 40rpx 40rpx;
}

.claim-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 60rpx;
  width: 100%;
}

.claim-item:last-child {
  margin-bottom: 0;
}

.claim-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  flex: 1;
}

.claim-value {
  color: #fff;
  font-size: 56rpx;
  font-weight: 700;
  margin-bottom: 8rpx;
  line-height: 1.2;
}

.claim-label {
  color: #999999;
  font-size: 28rpx;
  font-weight: 400;
  line-height: 1.2;
}

.claim-btn {
  background: #02F6F6;
  color: #000;
  border-radius: 30rpx;
  width: 200rpx;
  height: 70rpx;
  font-size: 30rpx;
  font-weight: 600;
  border: none;
  flex-shrink: 0;
  margin-left: 20rpx;
}

/* NFT购买弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: #1a1a1a;
  border-radius: 32rpx 32rpx 0 0;
  width: 100%;
  max-width: 750rpx;
  position: fixed;
  bottom: 0;
  padding: 0;
  max-height: 80vh;
  overflow-y: auto;
}

.modal-header {
  background: #02F6F6;
  border-radius: 32rpx 32rpx 0 0;
  padding: 40rpx;
  text-align: center;
}

.modal-title {
  color: #000;
  font-size: 32rpx;
  font-weight: 500;
}

.modal-body {
  padding: 40rpx;
}

.payment-section {
  margin-bottom: 30rpx;
}

.section-label {
  color: #fff;
  font-size: 28rpx;
  font-weight: 400;
  margin-bottom: 20rpx;
  display: block;
}

.payment-input {
  background: #333;
  border-radius: 16rpx;
  padding: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.amount-input {
  color: #fff;
  font-size: 32rpx;
  font-weight: 500;
  background: transparent;
  border: none;
  flex: 1;
}

.currency {
  color: #fff;
  font-size: 32rpx;
  font-weight: 500;
}

.add-section {
  text-align: center;
  margin: 30rpx 0;
}

.add-icon {
  color: #02F6F6;
  font-size: 40rpx;
  font-weight: 300;
}

.balance-section {
  background: #333;
  border-radius: 16rpx;
  padding: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.balance-input {
  color: #fff;
  font-size: 32rpx;
  font-weight: 500;
  background: transparent;
  border: none;
  flex: 1;
}

.infinity {
  color: #02F6F6;
  font-size: 32rpx;
  font-weight: 500;
}

.info-section {
  margin-bottom: 40rpx;
}

.usdt-balance {
  color: #fff;
  font-size: 26rpx;
  font-weight: 400;
}

.balance-value {
  color: #02F6F6;
  font-weight: 500;
}

.power-section {
  margin-bottom: 50rpx;
}

.power-title {
  color: #fff;
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 30rpx;
  display: block;
}

.power-row {
  display: flex;
  justify-content: space-between;
}

.power-item {
  flex: 1;
}

.power-label {
  color: #fff;
  font-size: 26rpx;
  margin-bottom: 16rpx;
  display: block;
}

.power-value {
  color: #02F6F6;
  font-weight: 600;
}

.confirm-btn {
  background: #02F6F6;
  color: #000;
  border-radius: 30rpx;
  width: 100%;
  height: 80rpx;
  font-size: 32rpx;
  font-weight: 600;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>