<template>

	<view class="me_profile">
		<!-- 顶部导航栏 -->
		<Header></Header>
		<view class="user_profile">
			<view class="user_tjgl">
				<view class="user_tjgl_boxlddq">
					<view class="user_tjgl_bq">{{ $t('_Ailk.recharge') }}</view>
					<view class="user_tjgl_ybq">
						<view class="user_tjgl_ybq1">Ailk-BEP20</view>
						<img class="user_tjgl_ybq5" src="../../static/ailkimg/xiabiaoxz.svg" alt="">
					</view>
				</view>
				<view class="user_tjgl_boxlddq">
					<view class="user_tjgl_bq">{{ $t('_Ailk.recharge_amount') }}</view>
				</view>
				<view class="tixian_tjgl_boxldbk">
					<input type="text" class="tixian_tjgl_boxldbk1"
						:placeholder="$t('_Ailk.please_enter_the_recharge_amount')" v-model="rechargeamount" />
				</view>
				<view class="user_tjgl_boxlddq">
					<!-- <view class="user_tjgl_bq">{{ $t('_Ailk.payment_required') }}</view> -->
					<view class="user_tjgl_bq"></view>
					<view class="user_tjgl_bq1">{{ $t('_Ailk.available') }}：{{ userasset.amount }}Ailk</view>
				</view>
				<!-- <view class="tixian_tjgl_boxldbk">
					<input type="text" class="tixian_tjgl_boxldbk1" placeholder="0.00" v-model="paymentamount" />
					<view class="tixian_tjgl_boxldbk4">{{ $t('_Ailk.all') }}</view>
				</view> -->

				<!-- <button class="tixin_button" @click="postqrecharge">
					<view class="tixin_buttonqd">{{ $t('_Ailk.recharge') }}</view>
				</button> -->
				<button class="tixin_button" @click="postqrecharge" :disabled="isSubmitting">
					<view class="tixin_buttonqd">
						{{ isSubmitting ? $t('_Ailk.processing') : $t('_Ailk.recharge') }}
					</view>
				</button>
			</view>


		</view>

	</view>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import Header from '../../components/common/header.vue';
import { getMemberInfo, qrecharge, qasyncRecharge, getBlockchainConfig } from '../../api/ailk.js';
import $web3 from "../../utils/web3/bsc.js";

const userasset = ref({}); // 用户资产
const blockchain = ref({}); // 区块链配置
const rechargeorder = ref({}); // 充值订单
const rechargeamount = ref(''); // 充值金额
// const paymentamount = ref(''); // 需支付金额
const isSubmitting = ref(false); // 防止重复提交的状态


// 跳转函数
const transferaccounts = () => {
	uni.navigateTo({
		url: '/pages/transferaccounts/transferaccounts'
	});
};

// 组件挂载后执行
onMounted(() => {
	getquserInfos();
	getqChinaInfo();
});
// 获取用户资产
const getquserInfos = async () => {
	const params = {

	};
	let res = await getMemberInfo(params)
	// console.log("获取用户资产:", res);
	if (res?.code === 200) {
		userasset.value = res?.data;
		console.log("获取用户资产:", userasset.value);
	} else {
		console.log({ message: res?.msg || "数据获取失败", type: "fail" });
	}
};
// 获取区块链配置
const getqChinaInfo = async () => {
	const params = {

	};
	let res = await getBlockchainConfig(params)
	// console.log("获取区块链配置:", res);
	if (res?.code === 200) {
		blockchain.value = res?.data[0];
		console.log("获取区块链配置:", blockchain.value);
	} else {
		console.log({ message: res?.msg || "数据获取失败", type: "fail" });
	}
};

// 充值
// const postqrecharge = async () => {

// 	// 参数校验逻辑
// 	if (!rechargeamount.value || isNaN(rechargeamount.value) || Number(rechargeamount.value) <= 0) {
// 		uni.showToast({
// 			title: `充值金额必须为大于0的数字`,
// 			icon: 'error',
// 			duration: 1000
// 		});
// 		return;
// 	}
// 	// if (!paymentamount.value?.trim()) {
// 	// 	uni.showToast({
// 	// 		title: '支付金额不能为空',
// 	// 		icon: 'error',
// 	// 		duration: 1000
// 	// 	});
// 	// 	return;
// 	// }

// 	// 处理充值
// 	const transferRes = await $web3.build_transfer({
// 		name: 'usdt',
// 		fromAccount: sessionStorage.getItem('signature'),
// 		toAccount: blockchain.value.collect_address,
// 		contract: blockchain.value.token_contract,
// 		quantity: Number(rechargeamount.value),
// 		abi: blockchain.value.token_abi,
// 	})
// 	console.log('交易哈希', transferRes);

// 	// 构造请求参数
// 	const params = {
// 		type: 1,
// 		amount: Number(rechargeamount.value),
// 		// pay_amount: paymentamount.value,
// 	};
// 	try {
// 		const res = await qrecharge(params);
// 		console.log('交易结果', res);
// 		console.log(res.code);
// 		console.log(res.data);
// 		if (res?.code === 200) {
// 			rechargeorder.value = res?.data;
// 			console.log("充值订单:", rechargeorder.value);
// 			uni.showToast({
// 				title: '充值订单创建成功',
// 				icon: 'success',
// 				duration: 1000
// 			});

// 			// 异步充值
// 			const params = {
// 				amount: Number(rechargeamount.value),
// 				out_trade_no: rechargeorder.value?.out_trade_no,
// 				transfer_tx: transferRes,
// 			};
// 			const res1 = await qasyncRecharge(params);
// 			if (res1?.code === 200) {
// 				uni.showToast({
// 					title: '充值成功',
// 					icon: 'success',
// 					duration: 1000
// 				});
// 				setTimeout(() => {
// 					location.reload();
// 				}, 100);
// 			} else {
// 				uni.showToast({
// 					title: res1?.msg || '请求异常',
// 					icon: 'error',
// 					duration: 1000
// 				});
// 			}

// 			// 可选：成功后重置表单
// 			rechargeamount.value = '';
// 			// paymentamount.value = '';
// 		} else {
// 			uni.showToast({
// 				title: res?.msg || '请求异常',
// 				icon: 'error',
// 				duration: 1000
// 			});
// 		}
// 	} catch (error) {
// 		console.error('请求失败:', error);
// 		uni.showToast({
// 			title: '网络或服务器错误',
// 			icon: 'error',
// 			duration: 1000
// 		});
// 	}
// };

const postqrecharge = async () => {
	// 防重复提交检查
	if (isSubmitting.value) return;
	isSubmitting.value = true;

	try {
		// 参数校验逻辑
		if (!rechargeamount.value || isNaN(rechargeamount.value) || Number(rechargeamount.value) <= 0) {
			uni.showToast({
				title: `充值金额必须为大于0的数字`,
				icon: 'error',
				duration: 1000
			});
			return;
		}

		// 处理充值
		const transferRes = await $web3.build_transfer({
			name: 'usdt',
			fromAccount: sessionStorage.getItem('signature'),
			toAccount: blockchain.value.collect_address,
			contract: blockchain.value.token_contract,
			quantity: Number(rechargeamount.value),
			abi: blockchain.value.token_abi,
		})
		console.log('交易哈希', transferRes);

		// 构造请求参数
		const params = {
			type: 1,
			amount: Number(rechargeamount.value),
			// pay_amount: paymentamount.value,
		};
		try {
			const res = await qrecharge(params);
			console.log('交易结果', res);
			console.log(res.code);
			console.log(res.data);
			if (res?.code === 200) {
				rechargeorder.value = res?.data;
				console.log("充值订单:", rechargeorder.value);
				uni.showToast({
					title: '充值订单创建成功',
					icon: 'success',
					duration: 1000
				});

				// 异步充值
				const params = {
					amount: Number(rechargeamount.value),
					out_trade_no: rechargeorder.value?.out_trade_no,
					transfer_tx: transferRes,
				};
				const res1 = await qasyncRecharge(params);
				if (res1?.code === 200) {
					uni.showToast({
						title: '充值成功',
						icon: 'success',
						duration: 1000
					});
					setTimeout(() => {
						location.reload();
					}, 100);
				} else {
					uni.showToast({
						title: res1?.msg || '请求异常',
						icon: 'error',
						duration: 1000
					});
				}

				// 可选：成功后重置表单
				rechargeamount.value = '';
				// paymentamount.value = '';
			} else {
				uni.showToast({
					title: res?.msg || '请求异常',
					icon: 'error',
					duration: 1000
				});
			}
		} catch (error) {
			console.error('请求失败:', error);
			uni.showToast({
				title: '网络或服务器错误',
				icon: 'error',
				duration: 1000
			});
		}

	} catch (error) {
		console.error('请求失败:', error);
		uni.showToast({ title: '网络或服务器错误', icon: 'error', duration: 1000 });
	} finally {
		// 无论成功失败都重置状态
		isSubmitting.value = false;
	}
};

</script>
<style lang="scss" scoped>
.me_profile {
	height: 100%;
	width: 100%;

	.user_profile {
		color: #ffffff;
		padding: 32rpx;
		height: 100%;
		width: 100%;
		box-sizing: border-box;

		.user_tjgl {
			// width: 686rpx;
			margin: 40rpx 0;
			// height: 586rpx;
			background: #02142A;
			border-radius: 24rpx 24rpx 24rpx 24rpx;
			border: 2rpx solid #004676;
			padding: 0 0 40rpx;

			.user_tjgl_box {
				height: 44rpx;
				margin: 40rpx 0 0 40rpx;
				display: flex;
				align-items: center;

				.user_tjgl_img {
					width: 32rpx;
					height: 32rpx;
					margin: 0 0 4rpx 0;
				}

				.user_tjgl_dj {
					// width: 136rpx;
					// margin: 0 0 0 24rpx;
					height: 44rpx;
					font-family: PingFang SC, PingFang SC;
					font-weight: 400;
					font-size: 32rpx;
					color: #FFFFFF;
					line-height: 38rpx;
					text-align: left;
					font-style: normal;
					text-transform: none;
				}
			}

			.user_tjgl_boxlddq {
				height: 44rpx;
				margin: 30rpx 40rpx 36rpx;
				display: flex;
				align-items: center;
				justify-content: space-between;

				.user_tjgl_bq {
					// width: 160rpx;
					// width: 40%;
					height: 44rpx;
					font-family: PingFang SC, PingFang SC;
					font-weight: 400;
					font-size: 32rpx;
					color: #FFFFFF;
					line-height: 38rpx;
					text-align: left;
					font-style: normal;
					text-transform: none;
				}

				.user_tjgl_bq1 {
					height: 44rpx;
					font-family: PingFang SC, PingFang SC;
					font-weight: 400;
					font-size: 32rpx;
					color: #39EAFD;
					line-height: 38rpx;
					text-align: left;
					font-style: normal;
					text-transform: none;
				}

				.user_tjgl_bq2 {
					height: 44rpx;
					font-family: PingFang SC, PingFang SC;
					font-weight: 500;
					font-size: 32rpx;
					color: rgba(255, 255, 255, 0.8);
					line-height: 38rpx;
					text-align: left;
					font-style: normal;
					text-transform: none;
				}

				.user_tjgl_ybq {
					height: 44rpx;
					display: flex;
					align-items: center;

					.user_tjgl_ybq1 {
						// width: 104rpx;
						height: 44rpx;
						font-family: PingFang SC, PingFang SC;
						font-weight: 400;
						font-size: 32rpx;
						color: #FFFFFF;
						line-height: 38rpx;
						text-align: right;
						font-style: normal;
						text-transform: none;
					}

					.user_tjgl_ybq2 {
						// width: 204rpx;
						margin: 0 0 0 8rpx;
						height: 40rpx;
						font-family: PingFang SC, PingFang SC;
						font-weight: 400;
						font-size: 28rpx;
						color: #71A5CE;
						line-height: 33rpx;
						text-align: right;
						font-style: normal;
						text-transform: none;
					}

					.user_tjgl_ybq3 {
						margin: 0 0 0 8rpx;
						width: 96rpx;
						height: 48rpx;
						font-family: PingFang SC, PingFang SC;
						font-weight: 400;
						font-size: 24rpx;
						color: #71A5CE;
						line-height: 24rpx;
						text-align: center;
						font-style: normal;
						text-transform: none;
					}

					.user_tjgl_ybq4 {
						// width: 96rpx;
						height: 44rpx;
						font-family: PingFang SC, PingFang SC;
						font-weight: 400;
						font-size: 32rpx;
						color: #39EAFD;
						line-height: 38rpx;
						text-align: right;
						font-style: normal;
						text-transform: none;
					}

					.user_tjgl_ybq5 {
						width: 40rpx;
						height: 40rpx;
						margin: 0 0 0 22rpx;
					}

					.user_tjgl_ybqfk {
						width: 40rpx;
						height: 40rpx;
						border-radius: 8rpx;
						border: 2rpx solid #39EAFD;
						margin: 0 0 0 8rpx;
					}

					.user_tjgl_ybqimg {
						width: 48rpx;
						height: 48rpx;
					}
				}
			}

			.tixian_tjgl_boxldbk {
				// width: 638rpx;
				margin: 24rpx;
				padding: 0 26rpx;
				height: 92rpx;
				background: #02142A;
				border-radius: 24rpx 24rpx 24rpx 24rpx;
				border: 2rpx solid #004676;
				display: flex;
				align-items: center;
				justify-content: space-between;

				.tixian_tjgl_boxldbk1 {
					// width: 546rpx;
					height: 44rpx;
					font-family: PingFang SC, PingFang SC;
					font-weight: 400;
					font-size: 32rpx;
					color: #71A5CE;
					line-height: 38rpx;
					text-align: left;
					font-style: normal;
					text-transform: none;
				}

				.tixian_tjgl_boxldbk2 {
					// width: 546rpx;
					height: 44rpx;
					font-family: PingFang SC, PingFang SC;
					font-weight: 400;
					font-size: 32rpx;
					color: #39EAFD;
					line-height: 38rpx;
					text-align: left;
					font-style: normal;
					text-transform: none;
				}

				.tixian_tjgl_boxldbk3 {
					width: 40rpx;
					height: 40rpx;
				}

				.tixian_tjgl_boxldbk4 {
					// width: 546rpx;
					height: 44rpx;
					font-family: PingFang SC, PingFang SC;
					font-weight: 500;
					font-size: 32rpx;
					color: #ffffff;
					line-height: 38rpx;
					text-align: left;
					font-style: normal;
					text-transform: none;
				}
			}

			.user_tjgl_boxldbklddq {
				margin: 24rpx;
				padding: 0 26rpx;
				height: 172rpx;
				background: #02142A;
				border-radius: 24rpx 24rpx 24rpx 24rpx;
				border: 2rpx solid #004676;
				display: flex;
				flex-direction: column;

				.user_tjgl_boxldbklddqxh {
					margin: 28rpx 0 0;
					height: 44rpx;
					display: flex;
					align-items: center;
					justify-content: space-between;

					.user_tjgl_boxldbklddqla {
						height: 44rpx;
						display: flex;
						align-items: center;

						.user_tjgl_boxldbklddqla1 {
							height: 44rpx;
							font-family: PingFang SC, PingFang SC;
							font-weight: 400;
							font-size: 32rpx;
							color: #71A5CE;
							line-height: 38rpx;
							text-align: left;
							font-style: normal;
							text-transform: none;
						}

						.user_tjgl_boxldbklddqla2 {
							height: 44rpx;
							font-family: PingFang SC, PingFang SC;
							font-weight: 400;
							font-size: 32rpx;
							color: #39EAFD;
							line-height: 38rpx;
							text-align: left;
							font-style: normal;
							text-transform: none;
						}
					}

					.user_tjgl_boxldbklddqimg {
						width: 44rpx;
						height: 44rpx;
					}

				}



			}

		}

		.tixin_button {
			// width: 640rpx;
			margin: 48rpx 27rpx 0;
			height: 70rpx;
			background: #3E89FF;
			border-radius: 8rpx;
			display: flex;
			align-items: center;
			justify-content: center;

			.tixin_buttonqd {
				// width: 120rpx;
				height: 30rpx;
				font-family: PingFang SC, PingFang SC;
				font-weight: 600;
				font-size: 30rpx;
				color: #FFFFFF;
				line-height: 30rpx;
				text-align: center;
				font-style: normal;
				text-transform: none;
			}
		}
	}
}
</style>