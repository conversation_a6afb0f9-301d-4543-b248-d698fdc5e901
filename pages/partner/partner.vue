<template>
	<!-- <view class="xt">
		<xtcode v-model='code' @complete="complete" @confirm="confirm"></xtcode>
		<xtcode type="bottom" @complete="complete" @confirm="confirm"></xtcode>
		<xtcode type="middle" @complete="complete" @confirm="confirm"></xtcode>
	</view> -->
	<view class="choujian-background">
		<!-- 顶部导航栏 -->
		<Header></Header>
		<view class="choujian">
			<view class="lunbotu">
				<img class="lunbotuimg" src="../../static/ailkimg/xingyunzhuanpan.png" alt="" />
				<view class="turntable_box">
					<q-turntable ref="turntable" :probabilities="[1.0, 0.0, 0.0, 0.0, 0.0, 0.0]" :areaNumber="6"
						@success="turntableSuccess"></q-turntable>
				</view>
				<view class="the_jin">{{ $t('_Ailk.today_dynamic') }}</view>
				<view class="xiaofeier">
					<view class="xiaofeier_x">{{ $t('_Ailk.consumption_amount') }}</view>
					<view class="xiaofeier_y">
						<!-- <input class="xiaofeier_yq" type="number" v-model="consumption"
							:placeholder="$t('_Ailk.enter_consumption')" @input="(e) => handleInput(e, 'consumption')"> -->
						<input class="xiaofeier_yq" type="text" :value="consumption"
							:placeholder="$t('_Ailk.enter_consumption')"
							@input="(e) => handleInput(e, 'consumption')" />
						<view class="xiaofeier_ya">U≈{{ cailkcurrency.price }}Ailk</view>
					</view>
				</view>
				<view class="xiaofeier">
					<view class="xiaofeier_x">{{ $t('_Ailk.discount_amount') }}</view>
					<view class="xiaofeier_y">
						<!-- <input class="xiaofeier_yq" type="number" v-model="discount"
							:placeholder="$t('_Ailk.enter_discount')" @input="(e) => handleInput(e, 'discount')"> -->
						<input class="xiaofeier_yq" type="text" :value="discount"
							:placeholder="$t('_Ailk.enter_discount')" @input="(e) => handleInput(e, 'discount')" />
						<view class="xiaofeier_ya">U≈{{ dailkcurrency.price }}Ailk</view>
					</view>
				</view>
				<view class="yidenjian">{{ $t('_Ailk.first_prize') }}</view>
				<view class="yidenjian">{{ $t('_Ailk.second_prize') }}</view>
				<view class="yidenjian">{{ $t('_Ailk.third_prize') }}</view>
				<view class="choujianbut">
					<view @click="turntableStart" class="choujiante">{{ $t('_Ailk.draw') }}</view>
				</view>
			</view>
			<view class="jianchizt">
				<view class="jianchizt_dw">{{ $t('_Ailk.pool_status') }}</view>
				<view class="jianchizt_yegsmg">
					<img class="jianchizt_ye1" src="../../static/ailkimg/jiangchiyuer.png" alt="">
					<view class="jianchizt_ye2">{{ $t('_Ailk.current_balance') }}{{
						advancedFormat(awardPool?.now_award_pool) }} Ailk</view>
				</view>
				<view class="jianchizt_yegsmg">
					<img class="jianchizt_ye1" src="../../static/ailkimg/zongushu.png" alt="">
					<view class="jianchizt_ye2">{{ $t('_Ailk.total_shares') }}{{
						advancedFormat(awardPool?.award_pool_total) }} {{ $t('_Ailk.strand') }}</view>
				</view>
				<view class="jianchizt_yegsmg">
					<img class="jianchizt_ye1" src="../../static/ailkimg/meigujz.png" alt="">
					<view class="jianchizt_ye2">{{ $t('_Ailk.per_share_value') }}{{
						advancedFormat(awardPool?.every_award_pool) }} Ailk</view>
				</view>
				<view class="jianchizt_jj">
					<!-- <view class="jianchizt_jj1">{{
						advancedFormat(awardPool?.every_award_pool) }}Ailk/1{{ $t('_Ailk.strand') }}</view> -->
					<view class="jianchizt_jj1"></view>
					<!-- <view class="jianchizt_jjbnt">
						<view class="jianchizt_jjbnt2"><img class="jianchizt_jj2img"
								src="../../static/ailkimg/jcremove.png" alt=""></view>
						<view class="jianchizt_jjbnte"> <input type="text" placeholder="" class="cyctrqyinput"
								v-model="tuigus" /> </view>
						<view class="jianchizt_jjbnt2"><img class="jianchizt_jj2img"
								src="../../static/ailkimg/jcadd.png" alt=""></view>
					</view> -->
					<view class="jianchizt_jjbnt">
						<view class="jianchizt_jjbnt2" @click="decrement">
							<img class="jianchizt_jj2img" src="../../static/ailkimg/jcremove.png" alt="">
						</view>
						<view class="jianchizt_jjbnte">
							<input type="text" placeholder="" class="cyctrqyinput" v-model="tuigus"
								@input="handleTuiguInput">
						</view>
						<view class="jianchizt_jjbnt2" @click="increment">
							<img class="jianchizt_jj2img" src="../../static/ailkimg/jcadd.png" alt="">
						</view>
					</view>
				</view>
				<view class="jianchizt_rc">
					<view class="jianchizt_rc2" @click="getqinvests"><text>{{ $t('_Ailk.invest') }}</text></view>
					<view class="jianchizt_rc2" @click="getqwithdraw"><text>{{ $t('_Ailk.withdraw') }}</text></view>
				</view>
			</view>
			<view class="choujianjilu">{{ $t('_Ailk.my_records') }}</view>
			<view class="choujianjiluxhx"></view>
			<view class="choujianjilusjj">
				<view class="choujianjilusjj_db">
					<text>{{ $t('_Ailk.time') }}</text>
					<text>{{ $t('_Ailk.result') }}</text>
					<text>{{ $t('_Ailk.reward') }}</text>
				</view>
				<view class="choujianjilusjj_hr"></view>
				<view class="choujianjilusjj_xb" v-for="(item, index) in qdiscount" :key="index">
					<text>{{ item.created_at }}</text>
					<text>{{ item.prize_name }}</text>
					<text>{{ item.prize_probability }}</text>
				</view>
			</view>

		</view>
	</view>
	<!-- 添加加载状态提示 -->
	<view class="loading-tips">
		<text v-if="pageParams.isLoading">{{ $t('_Ailk.loading') }}</text>
		<text v-else-if="!pageParams.hasMore">{{ $t('_Ailk.no_more_data') }}</text>
	</view>
	<!-- 中奖弹窗 -->
	<view v-if="thefirstprize" class="month_picker_mask" @click="thefirstprize = false">
		<view class="month_picker_mask1">
			<img src="/static/ailkimg/jianpintu2.svg" alt="" />
		</view>
	</view>
	<view v-if="secondprize" class="month_picker_mask" @click="secondprize = false">
		<view class="month_picker_mask1">
			<img src="/static/ailkimg/jianpintu3.svg" alt="" />
		</view>
	</view>
	<view v-if="thirdprize" class="month_picker_mask" @click="thirdprize = false">
		<view class="month_picker_mask1">
			<img src="/static/ailkimg/jianpintu4.svg" alt="" />
		</view>
	</view>
</template>

<script setup>
import { reactive, toRefs, ref, onMounted, onUpdated } from 'vue';
import Header from '../../components/common/header.vue';
import xtcode from "../../uni_modules/xt-code/components/xt-code/xt-code.vue";
import { onReachBottom } from '@dcloudio/uni-app';
import { qDrawLottery, qGetPrizes, qgetLotteryRecords, qawardPoolStatus, qwithdraw, qinvest, qgetRmpice } from "/api/ailk.js";
const data = reactive({
	award: 1, // 中奖编号
	// awardList: [
	// 	{
	// 		title: '谢谢光临'
	// 	},
	// 	{
	// 		title: '三等奖'
	// 	},
	// 	{
	// 		title: '二等奖'
	// 	},
	// 	{
	// 		title: '谢谢光临'
	// 	},
	// 	{
	// 		title: '三等奖'
	// 	},
	// 	{
	// 		title: '一等奖'
	// 	}
	// ] // 顺时针对应每个奖项
	awardList: [
		{
			title: '一等奖'
		},
		{
			title: '二等奖'
		},
		{
			title: '三等奖'
		},
		{
			title: '谢谢光临'
		},
		{
			title: '三等奖'
		},
		{
			title: '谢谢光临'
		}
	] // 顺时针对应每个奖项
});
const consumption = ref('');//消费金额
const discount = ref('');//折扣金额
const turntable = ref(null);
const qaward = ref('');
const qdiscount = ref([]);//抽奖记录
const awardPool = ref("");//奖池数据
const tuigus = ref("0");//入/退 股输入金额
const { award, awardList } = toRefs(data);
const dailkcurrency = ref(0);//兑换币
const cailkcurrency = ref(0);//兑换币
const thefirstprize = ref(false);
const secondprize = ref(false);
const thirdprize = ref(false);

const code = ref('');
const complete = (e) => {
	console.log("complete:", e);
};
const confirm = (e) => {
	console.log("confirm:", e);
};

// 格式化千分位数
const advancedFormat = (value, options = {}) => {
	if (value == null || value === "") return options.placeholder || "--";

	const num = Number(String(value).replace(/,/g, ''));
	if (isNaN(num)) return options.placeholder || "--";

	return num.toLocaleString('en-US', {
		minimumFractionDigits: options.decimals || 0,
		maximumFractionDigits: options.decimals ?? 2,
		style: options.currency ? 'currency' : undefined,
		currency: options.currency || undefined
	});
};

const handleInput = (e, field) => {
	// console.log("e:", e);
	// console.log("field:", field);
	// 获取输入值
	let value = e.detail.value;

	// 场景1：检测到输入负号时立即拦截
	if (value.startsWith('-')) {
		if (field === 'discount') {
			setTimeout(() => {
				discount.value = "";
			}, 100);
		} else {
			setTimeout(() => {
				consumption.value = "";
			}, 100);
		}
		uni.showToast({ title: '不能输入负数', icon: 'error' });
		// return;
	}

	// 场景2：过滤非数字和小数点
	let filteredValue = value
		.replace(/[^\d.]/g, '')
		.replace(/\.{2,}/g, '.')
		.replace(/^\./g, '')
		.replace(/(\.\d{2})\d+/, '$1');

	// 场景3：数值不能小于0
	const numericValue = Number(filteredValue) || 0;
	if (numericValue <= 0) {
		if (field === 'discount') {
			setTimeout(() => {
				discount.value = "";
			}, 100);
		} else {
			setTimeout(() => {
				consumption.value = "";
			}, 100);
		}
		uni.showToast({ title: '数值不能小于0', icon: 'error' });
	}

	// 场景4：消费金额需大于折扣金额
	if (field === 'discount' && Number(consumption.value) <= Number(discount.value)) {
		uni.showToast({ title: '消费金额需大于折扣', icon: 'error' });
	}

	// 限制整数部分不超过7位，超过则禁止输入
	let [intPart, decimalPart] = filteredValue.split('.');
	if (intPart.length > 7) {
		console.log("filteredValue", filteredValue);
		filteredValue = intPart.slice(0, 7);
		console.log("filteredValue", filteredValue);

		if (field === 'discount') {
			discount.value = "";
			setTimeout(() => {
				discount.value = filteredValue;
			}, 100);
		} else {
			consumption.value = "";
			setTimeout(() => {
				consumption.value = filteredValue;
			}, 100);
		}

		uni.showToast({ title: '最多输入7位数字', icon: 'error' });
		return; // 直接禁止输入
	}
	filteredValue = decimalPart !== undefined ? `${intPart}.${decimalPart}` : intPart;

	if (field === 'discount') {
		discount.value = filteredValue;
		if (filteredValue && Number(filteredValue) > 0) {
			DiscountailkPrice(filteredValue);
		} else {
			dailkcurrency.value = { price: 0 };
		}
	} else {
		consumption.value = filteredValue;
		if (filteredValue && Number(filteredValue) > 0) {
			ConsumeailkPrice(filteredValue);
		} else {
			cailkcurrency.value = { price: 0 };
		}
	}

};

// 修改接口方法，支持传入金额

const ConsumeailkPrice = async (amount) => {
	const params = { amount };
	let res = await qgetRmpice(params);
	if (res?.code === 200) {
		cailkcurrency.value = res?.data;
	} else {
		cailkcurrency.value = { price: 0 };
	}
};

const DiscountailkPrice = async (amount) => {
	const params = { amount };
	let res = await qgetRmpice(params);
	if (res?.code === 200) {
		dailkcurrency.value = res?.data;
	} else {
		dailkcurrency.value = { price: 0 };
	}
};

const turntableStart = async () => {
	try {
		// 步骤1：检查输入合法性
		if (!discount.value.trim() || !consumption.value.trim()) {
			console.log('消费金额与折扣金额不能为空', discount.value, consumption.value);
			uni.showToast({
				title: '请输入消费金额与折扣金额',
				icon: 'none',
				duration: 2000
			});
			return; // 关键修复点1：添加return中断后续执行
		}

		// 步骤2：数值有效性验证
		const discountNum = Number(discount.value);
		const consumptionNum = Number(consumption.value);

		if (discountNum < 0 || consumptionNum < 0) {
			uni.showToast({ title: '数值不能小于0', icon: 'none' });
			return; // 关键修复点2：负数拦截
		}

		// 步骤3：业务规则验证（消费金额需大于折扣）
		if (consumptionNum <= discountNum) {
			uni.showToast({ title: '消费金额需大于折扣', icon: 'none' });
			return; // 关键修复点3：逻辑关系拦截
		}

		// 步骤4：执行抽奖准备
		await getqDrawLottery();

		// 步骤5：获取奖项ID
		const awardId = qaward.value?.prize?.id;
		console.log("获奖信息:", qaward.value?.prize);
		console.log("awardId:", awardId);

		// 步骤6：执行转盘操作
		if (awardId !== undefined) {
			turntable.value.begin(awardId);
			discount.value = '';// 清空当前输入框
			consumption.value = '';// 清空当前输入框
		} else {
			console.error('未获取到有效的奖品ID');
			// uni.showToast({ title: '抽奖信息异常', icon: 'none' });
		}

	} catch (error) {
		console.error('抽奖流程异常:', error);
		uni.showToast({ title: '抽奖失败，请重试', icon: 'none' });
	}
};

// 定义中奖编号
const awardNumber = ref(null);
// 抽奖完成后操作
const turntableSuccess = (awardNumer) => {
	// const index = awardNumer - 1; // 根据中奖编号获取索引
	const index = awardNumer - 1; // 根据中奖编号获取索引
	console.log('中奖编号:', awardList.value);
	const awardTitle = data.awardList[index]?.title || '未知奖项'; // 获取对应的提示词
	uni.showToast({
		title: `${qaward.value?.prize?.description}`,
		icon: 'success'
	});
	console.log('中奖编号:', awardNumer, '奖项:', awardTitle, '提示词:', qaward.value?.prize?.description,);
	awardNumber.value = awardNumer; // 保存中奖编号
	if (awardNumber.value === 1) {
		thefirstprize.value = true;
	} else if (awardNumber.value === 2) {
		secondprize.value = true;
	} else if (awardNumber.value === 3) {
		thirdprize.value = true;
	}
};

// 抽奖商品数据
const getqGetPrizes = async () => {
	let res = await qGetPrizes()
	console.log("getqGetPrizes:", res);
	// if (res?.code === 200) {
	// 	games.value = res?.rows;
	// 	console.log("games:", games.value);
	// } else {
	// 	console.log({ message: res?.msg || "数据获取失败", type: "fail" });
	// }
};
// 中奖商品
const getqDrawLottery = async () => {
	const params = {
		consumption_amount: consumption.value,
		discount_amount: discount.value,
	};
	let res = await qDrawLottery(params)
	console.log("getqDrawLottery:", res);

	if (res?.code === 200) {
		qaward.value = res?.data;
		console.log("games:", qaward.value);
		getLoyRecords();
	} else {
		uni.showToast({
			title: res?.msg || "数据获取失败",
			icon: "error",
			duration: 1000,
		});
		console.log({ message: res?.msg || "数据获取失败", type: "fail" });
	}
	// getLoyRecords();
};

// 增减操作
const increment = () => {
	const num = Number(tuigus.value) || 0;
	tuigus.value = Math.max(num + 1, 0).toString();
};

const decrement = () => {
	const num = Number(tuigus.value) || 0;
	tuigus.value = Math.max(num - 1, 0).toString();
};

// 输入验证
const handleTuiguInput = (e) => {
	const value = e.detail.value;
	// 过滤非数字字符
	const filtered = value.replace(/[^\d]/g, '');
	// 防止负数
	tuigus.value = filtered === '' ? '0' : Math.abs(parseInt(filtered)).toString();
};

// 投入资金
const getqinvests = async () => {
	const params = {
		amount: tuigus.value,
	};

	const res = await qinvest(params)
	console.log("投入资金:", res);

	if (res?.code === 200) {
		// tramout.value = res?.data;
		// console.log("tramout:", tramout.value);
		await GawardPoolStatus();
		tuigus.value = "";
		setTimeout(() => {
			uni.showToast({
				title: "入股成功",
				icon: "success",
				duration: 1000,
			});
		}, 1000);

	} else {
		await GawardPoolStatus();
		console.log({ message: res?.msg || "数据获取失败", type: "fail" });
		setTimeout(() => {
			uni.showToast({
				title: res?.msg || "入股失败",
				icon: "error",
				duration: 2000,
			});
		}, 1000);

		tuigus.value = "";
	}
	// await getLoyRecords();

};

// 退出资金
const getqwithdraw = async () => {
	const params = {
		shares: tuigus.value,
	};

	let res = await qwithdraw(params)
	console.log("退出资金:", res);
	if (res?.code === 200) {
		await GawardPoolStatus();
		// 定时器
		uni.showToast({
			title: res?.msg || "撤股成功",
			icon: "success",
			duration: 1000,
		});
		tuigus.value = "";
	} else {
		console.log({ message: res?.msg || "数据获取失败", type: "fail" });
		await GawardPoolStatus();
		uni.showToast({
			title: res?.msg || "撤股失败",
			icon: "error",
			duration: 1000,
		});
		console.log("结束");

	}
	// await getLoyRecords();
};
// 当前奖池接口
const GawardPoolStatus = async () => {
	const params = {
	};
	let res = await qawardPoolStatus(params)
	console.log("当前奖池接口:", res);

	if (res?.code === 200) {
		awardPool.value = res?.data;
		console.log("awardPool:", awardPool.value);
	} else {
		console.log({ message: res?.msg || "数据获取失败", type: "fail" });
	}
};

// 分页相关变量
const pageParams = reactive({
	currentPage: 1,
	pageSize: 10,
	total: 0,
	isLoading: false,
	hasMore: true,
})

// 滚动加载生命周期
onReachBottom(() => {
	console.log("滚动加载更多", pageParams.isLoading, pageParams.hasMore);
	// if (!pageParams.isLoading && pageParams.hasMore) {
	// 	loadMore()
	// }
	loadMore();
})

// 加载更多方法
const loadMore = async () => {
	pageParams.currentPage++
	await getLoyRecords(true)
}

// 抽奖记录方法
const getLoyRecords = async (isLoadMore = false) => {
	console.log("isLoading:", pageParams.isLoading);
	console.log("hasMore:", pageParams.hasMore);
	try {
		pageParams.isLoading = true;
		// 关键修复：非加载更多时重置页码
		if (!isLoadMore) {
			pageParams.currentPage = 1
		}
		const params = {
			page: pageParams.currentPage,
			limit: pageParams.pageSize,
		}

		const res = await qgetLotteryRecords(params)
		if (res?.code === 200) {
			console.log("isLoadMore:", isLoadMore);
			if (isLoadMore) {
				qdiscount.value = [...qdiscount.value, ...res.data]
				console.log("isLoadMore++:", qdiscount.value);
			} else {
				qdiscount.value = res.data
				console.log("qdiscount:", qdiscount.value);
			}
			pageParams.total = res.total
			pageParams.hasMore = pageParams.currentPage * pageParams.pageSize < pageParams.total
		}
		console.log("抽奖商品记录:", res);
	} finally {
		pageParams.isLoading = false
		console.log("isLoading:", pageParams.isLoading);
		console.log("hasMore:", pageParams.hasMore);
	}
}

onMounted(() => {
	getqGetPrizes();
	getLoyRecords();
	GawardPoolStatus();
});

</script>

<style lang="scss" scoped>
.choujian-background {
	height: 100%;
	width: 100%;
}

.xt {
	height: 100%;
	padding: 50rpx;
	display: flex;
	flex-direction: column;
	gap: 50rpx;
	background-color: #e63dc9;
	color: #ffffff;
}

.choujian {
	position: relative;
	z-index: 1;
	padding: 30rpx;
	color: #ffffff;
	background-color: #000C1F;

	height: 100%;
	width: 100%;
	overflow-y: auto;
	box-sizing: border-box;

	.lunbotu {
		height: 1400rpx;
		background: #02142A;
		border-radius: 30rpx 30rpx 30rpx 30rpx;
		border: 2rpx solid #004676;

		.lunbotuimg {
			width: 492rpx;
			height: 156rpx;
			margin: 38rpx 96rpx 28rpx 100rpx;
		}

		.turntable_box {
			// width: 446rpx;
			// height: 446rpx;
		}

		.the_jin {
			// width: 128rpx;
			margin: 78rpx 26rpx 24rpx;
			height: 44rpx;
			font-family: PingFang SC, PingFang SC;
			font-weight: 400;
			font-size: 32rpx;
			color: #FFFFFF;
			line-height: 38rpx;
			text-align: left;
			font-style: normal;
			text-transform: none;
		}

		.xiaofeier {
			// width: 638rpx;
			margin: 24rpx;
			// height: 144rpx;
			padding: 0 0 24rpx;
			background: #001A42;
			border-radius: 24rpx 24rpx 24rpx 24rpx;
			border: 2rpx solid #004676;

			.xiaofeier_x {
				// width: 96rpx;
				margin: 24rpx 0 18rpx 26rpx;
				height: 34rpx;
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				font-size: 24rpx;
				color: #999999;
				line-height: 28rpx;
				text-align: left;
				font-style: normal;
				text-transform: none;
			}

			.xiaofeier_y {
				margin: 0 24rpx 0 26rpx;
				// height: 44rpx;
				display: flex;
				align-items: center;
				justify-content: space-between;
				gap: 8px;

				.xiaofeier_yq {
					width: 30%;
					// height: 44rpx;
					font-family: PingFang SC, PingFang SC;
					font-weight: 400;
					font-size: 24rpx;
					color: #FFFFFF;
					line-height: 38rpx;
					text-align: left;
					font-style: normal;
					text-transform: none;
				}

				.xiaofeier_ya {
					width: 70%;
					// height: 44rpx;
					font-family: PingFang SC, PingFang SC;
					font-weight: 400;
					font-size: 32rpx;
					color: #FFFFFF;
					line-height: 38rpx;
					text-align: right;
					font-style: normal;
					text-transform: none;
					overflow: hidden;
				}
			}
		}

		.yidenjian {
			// width: 224rpx;
			margin: 0 0 8rpx 38rpx;
			height: 34rpx;
			font-family: PingFang SC, PingFang SC;
			font-weight: 400;
			font-size: 24rpx;
			color: #999999;
			line-height: 28rpx;
			text-align: left;
			font-style: normal;
			text-transform: none;
		}

		.choujianbut {
			// width: 640rpx;
			margin: 14rpx 23rpx 0;
			height: 70rpx;
			background: #3E89FF;
			border-radius: 8rpx 8rpx 8rpx 8rpx;

			.choujiante {
				// width: 60rpx;
				height: 30rpx;
				font-family: PingFang SC, PingFang SC;
				font-weight: 600;
				font-size: 30rpx;
				color: #FFFFFF;
				line-height: 30rpx;
				text-align: center;
				font-style: normal;
				text-transform: none;
				padding: 20rpx 0 0 0;
			}
		}

	}

	.jianchizt {
		// width: 686rpx;
		margin: 40rpx 0;
		height: 540rpx;
		background: #02142A;
		border-radius: 24rpx 24rpx 24rpx 24rpx;
		border: 2rpx solid #004676;

		.jianchizt_dw {
			// width: 192rpx;
			margin: 40rpx 0 50rpx 24rpx;
			height: 44rpx;
			font-family: PingFang SC, PingFang SC;
			font-weight: 400;
			font-size: 32rpx;
			color: #FFFFFF;
			line-height: 38rpx;
			text-align: left;
			font-style: normal;
			text-transform: none;
		}

		.jianchizt_yegsmg {
			height: 50rpx;
			display: flex;
			align-items: center;
			margin: 0 0 22rpx 50rpx;

			.jianchizt_ye1 {
				width: 48rpx;
				height: 48rpx;
			}

			.jianchizt_ye2 {
				// width: 396rpx;
				margin: 0 0 0 16rpx;
				height: 44rpx;
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				font-size: 32rpx;
				color: #FFFFFF;
				line-height: 38rpx;
				text-align: left;
				font-style: normal;
				text-transform: none;
			}
		}

		.jianchizt_jj {
			height: 48rpx;
			display: flex;
			justify-content: space-between;
			padding: 0 24rpx 0;

			.jianchizt_jj1 {
				// width: 166rpx;
				height: 44rpx;
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				font-size: 32rpx;
				color: #FFFFFF;
				line-height: 38rpx;
				text-align: left;
				font-style: normal;
				text-transform: none;
			}

			.jianchizt_jjbnt {
				width: 184rpx;
				height: 48rpx;
				display: flex;

				.jianchizt_jjbnt2 {
					width: 48rpx;
					height: 48rpx;
					background: #E7E7E7;
					border-radius: 4rpx;

					.jianchizt_jj2img {
						width: 48rpx;
						height: 48rpx;
					}
				}

				.jianchizt_jjbnte {
					width: 72rpx;
					height: 48rpx;
					background: #E7E7E7;
					border-radius: 4rpx;
					margin: 0 8rpx 0;
					color: #000;
					display: flex;
					justify-content: center;
					align-items: center;
				}
			}
		}

		.jianchizt_rc {
			// width: 100%;
			margin: 32rpx 10rpx 0 24rpx;
			height: 70rpx;
			display: flex;

			.jianchizt_rc2 {
				margin: 0 14rpx 0 0;
				width: 312rpx;
				height: 70rpx;
				background: #001A42;
				border-radius: 8rpx;
				border: 2rpx solid #3E89FF;
				display: flex;
				justify-content: center;
				align-items: center;
				font-family: PingFang SC;
				font-weight: 600;
				font-size: 30rpx;
				color: #FFFFFF;
			}
		}
	}

	.choujianjilu {
		// width: 216rpx;
		height: 50rpx;
		font-family: PingFang SC, PingFang SC;
		font-weight: 600;
		font-size: 36rpx;
		color: #3E89FF;
		line-height: 42rpx;
		text-align: left;
		font-style: normal;
		text-transform: none;
	}

	.choujianjiluxhx {
		width: 216rpx;
		height: 4rpx;
		background: linear-gradient(270deg, rgba(244, 224, 179, 0) 0%, #3E89FF 54%, rgba(235, 216, 177, 0) 100%);
		border-radius: 0rpx 0rpx 0rpx 0rpx;
	}

	.choujianjilusjj {
		// width: 686rpx;
		margin: 40rpx 0 0 0;
		// height: 640rpx;
		background: #02142A;
		border-radius: 8rpx 8rpx 8rpx 8rpx;
		border: 2rpx solid #004676;

		.choujianjilusjj_db {
			// width: 60rpx;
			height: 42rpx;
			font-family: PingFang SC, PingFang SC;
			font-weight: 400;
			font-size: 30rpx;
			color: #3E89FF;
			line-height: 35rpx;
			text-align: center;
			font-style: normal;
			text-transform: none;
			display: flex;
			align-items: center;
			justify-content: space-around;
			padding: 30rpx 0 0;
		}

		.choujianjilusjj_hr {
			width: 604rpx;
			height: 0rpx;
			border: 2rpx solid #004676;
			opacity: 0.6;
			margin: 28rpx 0 50rpx 40rpx;
		}

		.choujianjilusjj_xb {
			// width: 134rpx;
			// height: 36rpx;
			font-family: PingFang HK, PingFang HK;
			font-weight: 500;
			font-size: 26rpx;
			color: #FFFFFF;
			line-height: 30rpx;
			text-align: center;
			font-style: normal;
			text-transform: none;
			// display: flex;
			// align-items: center;
			// justify-content:  space-between;
			padding: 10px 0;
			display: grid;
			grid-template-columns: repeat(3, 1fr);

			text {
				width: 100%;
			}
		}
	}

}

.loading-tips {
	height: 80rpx;
	display: flex;
	justify-content: center;
	align-items: center;
	color: #999;
	font-size: 24rpx;
}

.month_picker_mask {
	position: fixed;
	top: 0;
	left: 0;
	width: 100vw;
	height: 100vh;
	// background-color: rgba(0, 0, 0, 0.9);
	background-color: rgba(0, 0, 0);
	z-index: 9999;
	display: flex;
	align-items: center;
	justify-content: center;

	.month_picker_mask1 {
		width: 100%;
		height: 100%;
		background: url(/static/ailkimg/jianpintu1.svg) no-repeat top;

		// background-size: 100% 100%;
		// z-index: 9999;
		img {
			width: 100%;
			height: 100%;
		}
	}

}
</style>