<template>

	<view class="me_profile">
		<!-- 顶部导航栏 -->
		<Header></Header>
		<view class="user_profile">
			<view class="user_tjgl">
				<view class="user_tjgl_boxlddq">
					<view class="user_tjgl_bq">{{ $t('_Ailk.withdrawal') }}</view>
					<view class="user_tjgl_ybq">
						<view class="user_tjgl_ybq1">Ailk-BEP20</view>
						<img class="user_tjgl_ybq5" src="../../static/ailkimg/xiabiaoxz.svg" alt="">
					</view>
				</view>
				<!-- <view class="user_tjgl_boxlddq">
					<view class="user_tjgl_bq">{{ $t('_Ailk.withdrawal_address') }}</view>
				</view>
				<view class="tixian_tjgl_boxldbk">
					<input type="text" class="tixian_tjgl_boxldbk1"
						:placeholder="$t('_Ailk.enter_or_long_press_to_paste_the_address')"
						v-model="maskedUserAddress" />
					<img class="tixian_tjgl_boxldbk3" src="../../static/ailkimg/shaoma.svg" alt="">
				</view> -->
				<view class="user_tjgl_boxlddq">
					<view class="user_tjgl_bq">{{ $t('_Ailk.quantity') }}</view>
					<view class="user_tjgl_bq1">{{ $t('_Ailk.available') }}：{{ userasset?.amount }} Ailk</view>
				</view>
				<view class="tixian_tjgl_boxldbk">
					<input type="text" class="tixian_tjgl_boxldbk1"
						:placeholder="`${$t('_Ailk.minimum_withdrawal')}${wlcontion?.withdraw_min_amount}${$t('_Ailk.a_starting_point')}`"
						v-model="withdrawAmount" @input="(e) => Calculatewithdrawal(e, 'withdrawamount')" />
					<view class="tixian_tjgl_boxldbk4">{{ $t('_Ailk.all') }}</view>
				</view>
				<view class="user_tjgl_boxlddq">
					<view class="user_tjgl_bq2">{{ $t('_Ailk.commission') }}</view>
					<view class="user_tjgl_bq2">{{ commission }} Ailk</view>
				</view>
				<view class="user_tjgl_boxlddq">
					<view class="user_tjgl_bq2">{{ $t('_Ailk.quantity_received') }}</view>
					<view class="user_tjgl_bq2">{{ quantity_received }} Ailk</view>
				</view>
				<!-- <view class="user_tjgl_boxlddq">
					<view class="user_tjgl_bq">{{ $t('_Ailk.payment_password') }}</view>
				</view>
				<view class="tixian_tjgl_boxldbk" style="display: none;">
					<input type="password" class="tixian_tjgl_boxldbk1"
						:placeholder="$t('_Ailk.please_enter_the_payment_password')" v-model="paymentpassword" />
				</view> -->
			</view>

			<view class="baodansomin">
				<view class="baodansomin_tefr">* {{ $t('_Ailk.withdrawal_instructions') }}</view>
				<view class="baodansomin_cdcd">
					<!-- <view class="baodansomin_cdcdy"></view> -->
					<view class="baodansomin_cdcdt">{{ $t('_Ailk.withdrawal_tip1') }}</view>
				</view>
				<view class="baodansomin_cdcd">
					<view class="baodansomin_cdcdt">{{ $t('_Ailk.withdrawal_tip2') }}</view>
				</view>
				<!-- <view class="baodansomin_cdcd">
					<view class="baodansomin_cdcdt">3、每次提现最小数量为100个Ailk </view>
				</view>
				<view class="baodansomin_cdcd">
					<view class="baodansomin_cdcdt">4、申请提交成功后，我们将进行人工审核，请耐心等待审核结果。</view>
				</view> -->

			</view>

			<view class="tixin_button" @click="postqapplys">
				<view class="tixin_buttonqd">{{ $t('_Ailk.withdrawal') }}</view>
			</view>

		</view>

	</view>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import Header from '../../components/common/header.vue';
import { getMemberInfo, qapply, qconfig } from '../../api/ailk.js';

const maskedUserAddress = ref(''); // 提现地址
const paymentpassword = ref(''); // 支付密码
const userasset = ref({}); // 用户资产
const wlcontion = ref({}); // 提现配置信息
const withdrawAmount = ref(''); // 提现金额
const commission = ref(0); // 提现手续费
const quantity_received = ref(0); // 实际到账数量

// 跳转函数
const transferaccounts = () => {
	uni.navigateTo({
		url: '/pages/transferaccounts/transferaccounts'
	});
};

// 组件挂载后执行
onMounted(() => {
	getquserInfos();
	getqconfigs();
});
// 获取用户资产
const getquserInfos = async () => {
	const params = {

	};
	let res = await getMemberInfo(params)
	// console.log("获取用户资产:", res);

	if (res?.code === 200) {
		userasset.value = res?.data;
		console.log("用户资产userasset:", userasset.value);
	} else {
		console.log({ message: res?.msg || "失败", type: "fail" });
	}
};

// 输入框内容改变时执行
const Calculatewithdrawal = (e, field) => {
	console.log("e:", e);
	console.log("field:", field);
	let value = e.detail.value;

	// 场景1：检测到输入负号时立即拦截
	if (value.startsWith('-')) {
		uni.showToast({ title: '不能输入负数', icon: 'error' });
		withdrawAmount.value = '';
		return;
	}

	// 场景2：过滤非数字和小数点
	let filteredValue = value
		.replace(/[^\d.]/g, '')
		.replace(/\.{2,}/g, '.')
		.replace(/^\./g, '')
		.replace(/(\.\d{2})\d+/, '$1');

	
	// 场景3：数值不能小于0
	const numericValue = Number(filteredValue) || 0;
	if (numericValue <= 0) {
		uni.showToast({ title: '数值不能小于0', icon: 'error' });
		withdrawAmount.value = '';
		return;
	}

	// 更新字段值
	withdrawAmount.value = filteredValue;
	// 输入时自动调接口
	if (filteredValue && Number(filteredValue) > 0) {
		calculateCommission(filteredValue);
	} else {
		dailkcurrency.value = { price: 0 };
	}

};

// 提现手续费计算
const calculateCommission = async (amount) => {
	commission.value = Number(withdrawAmount.value) * 0.05 / 100;
	quantity_received.value = Number(withdrawAmount.value) - commission.value;
};

// 提现配置信息
const getqconfigs = async () => {
	const params = {

	};
	let res = await qconfig(params)
	// console.log("获取提现配置信息:", res);

	if (res?.code === 200) {
		wlcontion.value = res?.data;
		console.log("提现wlcontion:", wlcontion.value);
	} else {
		console.log({ message: res?.msg || "失败", type: "fail" });
	}
};

// 提现
// const postqapplys = async () => {
// 	const params = {
// 		type: 1,//资产类型 1:ailk
// 		amount: withdrawAmount.value,
// 		address: maskedUserAddress.value,
// 		pay_password: paymentpassword.value,
// 	};
// 	let res = await qapply(params)

// 	if (res?.code === 200) {
// 		console.log("提现wlcontion:", wlcontion.value);
// 		uni.showToast({
// 			title: '提现成功',
// 			icon: 'success',
// 			duration: 1000,
// 		});
// 	} else {
// 		console.log({ message: res?.msg || "失败", type: "fail" });
// 		uni.showToast({
// 			title: res?.msg || "提现失败",
// 			icon: 'error',
// 			duration: 1000,
// 		});
// 	}
// };
let isSubmitting = false;// 防止重复提交默认未提交
// 提现
const postqapplys = async () => {
	// console.log("提交状态:", isSubmitting);
	if (isSubmitting) return;
	// isSubmitting = true;
	// 参数校验逻辑
	if (!withdrawAmount.value || isNaN(withdrawAmount.value) || Number(withdrawAmount.value) <= 0) {
		uni.showToast({
			title: `提现金额必须为大于等于${wlcontion.value?.withdraw_min_amount}的数字`,
			icon: 'error',
			duration: 1000
		});
		return;
	}

	// if (!maskedUserAddress.value?.trim()) {
	// 	uni.showToast({
	// 		title: '钱包地址不能为空',
	// 		icon: 'error',
	// 		duration: 1000
	// 	});
	// 	return;
	// }

	// if (!paymentpassword.value?.trim()) {
	// 	uni.showToast({
	// 		title: '支付密码不能为空',
	// 		icon: 'error',
	// 		duration: 1000
	// 	});
	// 	return;
	// }

	// 业务规则校验（示例：最小提现金额）
	const MIN_WITHDRAW = wlcontion.value?.withdraw_min_amount;
	if (Number(withdrawAmount.value) < MIN_WITHDRAW) {
		uni.showToast({
			title: `最小提现金额为${MIN_WITHDRAW}`,
			icon: 'error',
			duration: 1000
		});
		return;
	}

	// 构造请求参数
	const params = {
		type: 1,
		amount: withdrawAmount.value,
		address: maskedUserAddress.value,
		pay_password: paymentpassword.value,
	};

	try {
		const res = await qapply(params);
		if (res?.code === 200) {
			uni.showToast({
				title: '提现成功',
				icon: 'success',
				duration: 1000
			});
			// 可选：成功后重置表单
			withdrawAmount.value = '';
			maskedUserAddress.value = '';
			paymentpassword.value = '';
			getquserInfos();
		} else {
			uni.showToast({
				title: res?.msg || '提现请求异常',
				icon: 'error',
				duration: 1000
			});
			getquserInfos();
		}
	} catch (error) {
		console.error('提现请求失败:', error);
		uni.showToast({
			title: '网络或服务器错误',
			icon: 'error',
			duration: 1000
		});
	} finally {
		isSubmitting = false;
		console.log("提交状态:", isSubmitting);
	}
};

</script>
<style lang="scss" scoped>
.me_profile {
	height: 100%;
	width: 100%;

	.user_profile {
		color: #ffffff;
		padding: 32rpx;
		height: 100%;
		width: 100%;
		box-sizing: border-box;

		.user_tjgl {
			// width: 686rpx;
			margin: 40rpx 0;
			// height: 586rpx;
			background: #02142A;
			border-radius: 24rpx 24rpx 24rpx 24rpx;
			border: 2rpx solid #004676;
			padding: 0 0 40rpx;

			.user_tjgl_box {
				height: 44rpx;
				margin: 40rpx 0 0 40rpx;
				display: flex;
				align-items: center;

				.user_tjgl_img {
					width: 32rpx;
					height: 32rpx;
					margin: 0 0 4rpx 0;
				}

				.user_tjgl_dj {
					// width: 136rpx;
					// margin: 0 0 0 24rpx;
					height: 44rpx;
					font-family: PingFang SC, PingFang SC;
					font-weight: 400;
					font-size: 32rpx;
					color: #FFFFFF;
					line-height: 38rpx;
					text-align: left;
					font-style: normal;
					text-transform: none;
				}
			}

			.user_tjgl_boxlddq {
				height: 44rpx;
				margin: 24rpx 40rpx 0;
				display: flex;
				align-items: center;
				justify-content: space-between;

				.user_tjgl_bq {
					// width: 160rpx;
					// width: 40%;
					height: 44rpx;
					font-family: PingFang SC, PingFang SC;
					font-weight: 400;
					font-size: 32rpx;
					color: #FFFFFF;
					line-height: 38rpx;
					text-align: left;
					font-style: normal;
					text-transform: none;
				}

				.user_tjgl_bq1 {
					height: 44rpx;
					font-family: PingFang SC, PingFang SC;
					font-weight: 400;
					font-size: 32rpx;
					color: #39EAFD;
					line-height: 38rpx;
					text-align: left;
					font-style: normal;
					text-transform: none;
				}

				.user_tjgl_bq2 {
					height: 44rpx;
					font-family: PingFang SC, PingFang SC;
					font-weight: 500;
					font-size: 32rpx;
					color: rgba(255, 255, 255, 0.8);
					line-height: 38rpx;
					text-align: left;
					font-style: normal;
					text-transform: none;
				}

				.user_tjgl_ybq {
					height: 44rpx;
					display: flex;
					align-items: center;

					.user_tjgl_ybq1 {
						// width: 104rpx;
						height: 44rpx;
						font-family: PingFang SC, PingFang SC;
						font-weight: 400;
						font-size: 32rpx;
						color: #FFFFFF;
						line-height: 38rpx;
						text-align: right;
						font-style: normal;
						text-transform: none;
					}

					.user_tjgl_ybq2 {
						// width: 204rpx;
						margin: 0 0 0 8rpx;
						height: 40rpx;
						font-family: PingFang SC, PingFang SC;
						font-weight: 400;
						font-size: 28rpx;
						color: #71A5CE;
						line-height: 33rpx;
						text-align: right;
						font-style: normal;
						text-transform: none;
					}

					.user_tjgl_ybq3 {
						margin: 0 0 0 8rpx;
						width: 96rpx;
						height: 48rpx;
						font-family: PingFang SC, PingFang SC;
						font-weight: 400;
						font-size: 24rpx;
						color: #71A5CE;
						line-height: 24rpx;
						text-align: center;
						font-style: normal;
						text-transform: none;
					}

					.user_tjgl_ybq4 {
						// width: 96rpx;
						height: 44rpx;
						font-family: PingFang SC, PingFang SC;
						font-weight: 400;
						font-size: 32rpx;
						color: #39EAFD;
						line-height: 38rpx;
						text-align: right;
						font-style: normal;
						text-transform: none;
					}

					.user_tjgl_ybq5 {
						width: 40rpx;
						height: 40rpx;
						margin: 0 0 0 22rpx;
					}

					.user_tjgl_ybqfk {
						width: 40rpx;
						height: 40rpx;
						border-radius: 8rpx;
						border: 2rpx solid #39EAFD;
						margin: 0 0 0 8rpx;
					}

					.user_tjgl_ybqimg {
						width: 48rpx;
						height: 48rpx;
					}
				}
			}

			.tixian_tjgl_boxldbk {
				// width: 638rpx;
				margin: 24rpx;
				padding: 0 26rpx;
				height: 92rpx;
				background: #02142A;
				border-radius: 24rpx 24rpx 24rpx 24rpx;
				border: 2rpx solid #004676;
				display: flex;
				align-items: center;
				justify-content: space-between;

				.tixian_tjgl_boxldbk1 {
					// width: 546rpx;
					height: 44rpx;
					font-family: PingFang SC, PingFang SC;
					font-weight: 400;
					font-size: 32rpx;
					color: #71A5CE;
					line-height: 38rpx;
					text-align: left;
					font-style: normal;
					text-transform: none;
				}

				.tixian_tjgl_boxldbk2 {
					// width: 546rpx;
					height: 44rpx;
					font-family: PingFang SC, PingFang SC;
					font-weight: 400;
					font-size: 32rpx;
					color: #39EAFD;
					line-height: 38rpx;
					text-align: left;
					font-style: normal;
					text-transform: none;
				}

				.tixian_tjgl_boxldbk3 {
					width: 40rpx;
					height: 40rpx;
				}

				.tixian_tjgl_boxldbk4 {
					// width: 546rpx;
					height: 44rpx;
					font-family: PingFang SC, PingFang SC;
					font-weight: 500;
					font-size: 32rpx;
					color: #ffffff;
					line-height: 38rpx;
					text-align: left;
					font-style: normal;
					text-transform: none;
				}
			}

			.user_tjgl_boxldbklddq {
				margin: 24rpx;
				padding: 0 26rpx;
				height: 172rpx;
				background: #02142A;
				border-radius: 24rpx 24rpx 24rpx 24rpx;
				border: 2rpx solid #004676;
				display: flex;
				flex-direction: column;

				.user_tjgl_boxldbklddqxh {
					margin: 28rpx 0 0;
					height: 44rpx;
					display: flex;
					align-items: center;
					justify-content: space-between;

					.user_tjgl_boxldbklddqla {
						height: 44rpx;
						display: flex;
						align-items: center;

						.user_tjgl_boxldbklddqla1 {
							height: 44rpx;
							font-family: PingFang SC, PingFang SC;
							font-weight: 400;
							font-size: 32rpx;
							color: #71A5CE;
							line-height: 38rpx;
							text-align: left;
							font-style: normal;
							text-transform: none;
						}

						.user_tjgl_boxldbklddqla2 {
							height: 44rpx;
							font-family: PingFang SC, PingFang SC;
							font-weight: 400;
							font-size: 32rpx;
							color: #39EAFD;
							line-height: 38rpx;
							text-align: left;
							font-style: normal;
							text-transform: none;
						}
					}

					.user_tjgl_boxldbklddqimg {
						width: 44rpx;
						height: 44rpx;
					}

				}



			}

		}

		.baodansomin {
			// width: 686rpx;
			// height: 242rpx;
			// background: #02142A;
			// border-radius: 24rpx;
			// border: 2rpx solid #004676;
			// padding: 40rpx 40rpx 40rpx 0;

			.baodansomin_tefr {
				margin: 0 0 20rpx;
				height: 44rpx;
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				font-size: 32rpx;
				color: #FFFFFF;
				line-height: 38rpx;
				text-align: left;
				font-style: normal;
				text-transform: none;
			}

			.baodansomin_cdcd {
				// height: 34rpx;
				display: flex;
				align-items: center;
				// margin: 0 0 24rpx;

				.baodansomin_cdcdy {
					width: 10rpx;
					height: 10rpx;
					background: #71A5CE;
					border-radius: 50%;
					margin: 0 10rpx 0 0;
				}

				.baodansomin_cdcdt {
					// width: 370rpx;
					// height: 34rpx;
					font-family: PingFang SC, PingFang SC;
					font-weight: 400;
					font-size: 28rpx;
					color: #FFFFFF;
					line-height: 48rpx;
					text-align: left;
					font-style: normal;
					text-transform: none;
				}
			}
		}

		.tixin_button {
			// width: 640rpx;
			margin: 48rpx 0 44rpx 0;
			height: 70rpx;
			background: #3E89FF;
			border-radius: 8rpx;
			display: flex;
			align-items: center;
			justify-content: center;

			.tixin_buttonqd {
				// width: 120rpx;
				height: 30rpx;
				font-family: PingFang SC, PingFang SC;
				font-weight: 600;
				font-size: 30rpx;
				color: #FFFFFF;
				line-height: 30rpx;
				text-align: center;
				font-style: normal;
				text-transform: none;
			}
		}
	}
}
</style>