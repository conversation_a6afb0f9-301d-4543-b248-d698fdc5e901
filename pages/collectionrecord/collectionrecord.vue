<template>
	<!-- 顶部导航栏 -->
	<Header></Header>
	<view class="collectionrecord">
		<view class="choujianjilu">{{ $t('_Ailk.claim_records') }}</view>
		<view class="choujianjiluxhx"></view>
		<view class="choujianjilusjj">
			<view class="choujianjilusjj_db">
				<text>{{ $t('_Ailk.time') }}</text>
				<text>{{ $t('_Ailk.type') }}</text>
				<text>{{ $t('_Ailk.reward') }}</text>
			</view>
			<view class="choujianjilusjj_hr"></view>
			<view class="choujianjilusjj_xb">
				<text>2025.04.01</text>
				<text>{{ $t('_Ailk.cross_pool') }}</text>
				<text>+50 Ailk</text>
			</view>
			<view class="choujianjilusjj_xb">
				<text>2025.04.01</text>
				<text>{{ $t('_Ailk.bet_pool') }}</text>
				<text>+50 Ailk</text>
			</view>
		</view>
	</view>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import Header from '../../components/common/header.vue';
</script>

<style lang="scss" scoped>
.collectionrecord{
	z-index: 1;
	padding: 30rpx;
	height: 100%;
	width: 100%;
	box-sizing: border-box;
}
.choujianjilu {
	// width: 216rpx;
	height: 50rpx;
	font-family: PingFang SC, PingFang SC;
	font-weight: 600;
	font-size: 36rpx;
	color: #3E89FF;
	line-height: 42rpx;
	text-align: left;
	font-style: normal;
	text-transform: none;
}

.choujianjiluxhx {
	width: 216rpx;
	height: 4rpx;
	background: linear-gradient(270deg, rgba(244, 224, 179, 0) 0%, #3E89FF 54%, rgba(235, 216, 177, 0) 100%);
	border-radius: 0rpx 0rpx 0rpx 0rpx;
}

.choujianjilusjj {
	// width: 686rpx;
	margin: 40rpx 0 0 0;
	height: 640rpx;
	background: #02142A;
	border-radius: 8rpx 8rpx 8rpx 8rpx;
	border: 2rpx solid #004676;

	.choujianjilusjj_db {
		// width: 60rpx;
		height: 42rpx;
		font-family: PingFang SC, PingFang SC;
		font-weight: 400;
		font-size: 30rpx;
		color: #3E89FF;
		line-height: 35rpx;
		text-align: center;
		font-style: normal;
		text-transform: none;
		display: flex;
		align-items: center;
		justify-content: space-around;
		padding: 30rpx 0 0;
	}

	.choujianjilusjj_hr {
		width: 604rpx;
		height: 0rpx;
		border: 2rpx solid #004676;
		opacity: 0.6;
		margin: 28rpx 0 50rpx 40rpx;
	}

	.choujianjilusjj_xb {
		// width: 134rpx;
		height: 36rpx;
		font-family: PingFang HK, PingFang HK;
		font-weight: 500;
		font-size: 26rpx;
		color: #FFFFFF;
		line-height: 30rpx;
		text-align: center;
		font-style: normal;
		text-transform: none;
		display: flex;
		align-items: center;
		justify-content: space-around;
		margin: 0 0 42rpx 0;
	}
}
</style>
