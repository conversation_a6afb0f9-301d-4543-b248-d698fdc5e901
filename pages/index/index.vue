<template>
	<view class="container">
		<!-- 顶部导航栏 -->
		<Header :on-login="dengdai"></Header>
		<view class="home-background">
			<view class="home">
				<view class="home-box">
					<view class="home-title-box">
					<view class="home-title">Emma <span class="select">Finance</span></view>
					<view class="sub-title">艾玛「EMR」</view>
				</view>	
				<view class="home-desc-box">
					<view class="desc">项目概述</view>
					<view class="sub-desc">Emma Finance</view>
				</view>
				<view class="home-content">
					<view>
						<view class="home-content-title"><span></span>核心使命</view>
						<view>
							打破传统金融与新兴资产间的壁垒，通过技术整合实现金融资源的平等化与民主化。
						</view>
					</view>
					<view>
						<view class="home-content-title"><span></span>发展愿景</view>
						<view>
							构建全球首个跨资产智能金融枢纽，成为用户管理全维度财富的统一入口。
						</view>
					</view>
				</view>
			</view>
				
			</view>
		</view>
	</view>
</template>

<script>
import Header from '/components/common/header.vue';
import Web3 from "web3";
import $web3 from "../../utils/web3/bsc.js";
import { login, getBlockchainConfig,qlistNewcomer, qtodayEvent } from "../../api/ailk.js";
import { ethers } from "../../node_modules/ethers";
export default {
	components: {
		Header
	},
	data() {
		return {
			products: [],
			article_category: [],
			articles: [],
			news: [1, 2, 3],
			newsTabs: ['币圈头条', '模式制度', '赚钱攻略'],
			currentNewsTab: 0,
			currentCateId: '',
			num: 1,
			isLoading: false,
			isInitialized: false,
			contract_address: '',
			abi: '',
			to_address: '',
			pay_type: 1, // 支付类型
			minAmount: 1,
			maxAmount: 10000,
			message: '',
			signature: '',
			invite_code: '',
			notice: {},
			banner: {
				path: '../../static/images/banner.png'
			},
			chain: null,
			address: '',
			xszlxq: [],
			jrdtsj: {},
			todayEventsTimer: null,
			// 分页相关变量
			pageParams: {
				currentPage: 1,
				pageSize: 10,
				total: 0,
				isLoading: false,
				hasMore: true,
			},
		}
	},
	onLoad() {
		
	},
	onShow() {
		this.qgetlistNewcomer();
		this.getqtodayEvents();

		console.log("isInitialized", this.isInitialized);
		this.dengdai();
	},
	mounted() {
		if (this.$route.query.invite_code != undefined) {
			this.invite_code = this.$route.query.invite_code;
		}
		if (!sessionStorage.getItem('lang')) {
			sessionStorage.setItem('lang', 'cn')
		}
		this.baseUrl = "https://ailkdapp.tocoinweb3.com/";
		// this.getBuyConfig()
		// this.login();
		// 页面加载时执行
		this.initPageData();
		this.qgetlistNewcomer();
		this.getqtodayEvents();
	},
	methods: {
		// 跳转
		goToconjian() {
			uni.switchTab({ url: '/pages/partner/partner' })
		},
		goTofenhong() {
			uni.switchTab({ url: '/pages/team/team' })
		},
		goTobaodan() {
			uni.switchTab({ url: '/pages/community/community' })
		},
		// 刷新页面
		refreshPage() {
			this.initPageData() // 刷新页面数据
		},
		// 今日动态接口
		async getqtodayEvents() {
			let res = await qtodayEvent()
			if (res?.code === 200) {
				this.jrdtsj = res?.data;
				console.log("今日动态:", this.jrdtsj);
			} else {
				console.log({ message: res?.msg || "数据获取失败", type: "fail" });
			}
		},
		async dengdai() {
			if (!this.isInitialized) {
				this.initPageData()
			}
		},

		onReachBottom() {
			// if (this.pageParams.hasMore && !this.pageParams.isLoading) {
				this.pageParams.currentPage++;
				this.qgetlistNewcomer(true);
			// }
		},

		// 新手指南接口
		qgetlistNewcomer(isLoadMore = false) {
			if (isLoadMore) {
				this.pageParams.isLoading = true;
			}
			const params = {
				page: this.pageParams.currentPage,
				pageSize: this.pageParams.pageSize,
				cate_id: this.currentCateId
			};
			qlistNewcomer(params).then(res => {
				console.log("新手指南:", res);
				if (res?.code === 200) {
					if (isLoadMore) {
						this.xszlxq.push(...res?.data?.data);
					} else {
						this.xszlxq = res?.data?.data;
					}
					this.pageParams.total = res?.total;
					this.pageParams.hasMore = this.xszlxq.length < this.pageParams.total;
				} else {
					console.log({ message: res?.msg || "数据获取失败", type: "fail" });
				}
			}).finally(() => {
				if (isLoadMore) {
					this.pageParams.isLoading = false;
				}
			});
		},

		async initPageData() {
			if (this.isLoading) return

			try {
				this.isLoading = true
				uni.showLoading({
					title: '加载中',
					mask: true
				})

				const [blockchainRes,] = await Promise.all([
					getBlockchainConfig().catch(err => ({ data: { data: [] } })),
				])
				console.log("blockchainRes原始返回", blockchainRes);
				if (blockchainRes.data?.[0]) {
					const data = blockchainRes.data[0]
					this.chain = data.chain
					this.abi = data.token_abi
					this.contract_address = data.token_contract
					this.to_address = data.collect_address
					await this.login().catch(err => {
						console.error('钱包连接失败:', err)
					})
				}

				this.isInitialized = true

			} catch (error) {
				console.error('初始化数据失败:', error)
				uni.showToast({
					title: '数据加载失败',
					icon: 'error'
				})
			} finally {
				this.isLoading = false
				uni.hideLoading()
			}
		},
		async login() {
			
			this.connectWallet();
			if (!sessionStorage.getItem("token")) {
				const web3 = new Web3(window.ethereum);
				this.getBlockchainConfig()
				this.switchNet(web3);
				let signer = await this.getSigner();
				const message = this.$t('index.text7');
				try {
					const signature = await signer.signMessage(message);
					console.log(signature, "signature");
				} catch (e) {
					uni.showToast({
						title: this.$t('index.text8'),
						icon: "none",
					});
					return;
				}
				// const formData = new FormData();
				// formData.append("address", this.address);
				// formData.append("inviteCode", this.inviteCode);
				let code = this.$route.query.invite_code;
				if (code === undefined) {
					code = ''
				}

				let res = await login({
					address: this.address,
					code: code,
				});
				console.log(res, "res");
				if (res.code === 200) {
					console.log(res.data);
					sessionStorage.setItem("token", res.data.token);
					sessionStorage.setItem("address", this.address);
					sessionStorage.setItem("userInfo", JSON.stringify(res.data.info));
					location.reload();
				} else {
					uni.showToast({
						title: res.msg,
						icon: "error",
					});
				}
			}
		},
		async getSigner() {
			return (await this.getProvider()).getSigner();
		},

		async getProvider() {
			let provider;
			if (window.ethereum == null) {
				provider = ethers.getDefaultProvider();
			} else {
				provider = new ethers.BrowserProvider(window.ethereum);
			}

			return provider;
		},
		async switchNet(web3) {
			// await this.getBuyConfig()
			console.log(this.chain+'----chain---');
			console.log(window.ethereum.chainId+'==chainId==')
			if (this.chain === 2) {
				if (window.ethereum.chainId != 0x38) {
					// 要添加的网络信息
					const newNetworkConfig = {
						chainId: "0x38", // 以十六进制表示的链 ID
						chainName: "BNB Smart Chain",
						nativeCurrency: {
							name: "BNB",
							symbol: "BNB",
							decimals: 18,
						},
						rpcUrls: ["https://rpc.ankr.com/bsc"],
						blockExplorerUrls: ["https://bscscan.com"],
					};

					try {
						// 添加新网络到 MetaMask
						await window.ethereum.request({
							method: "wallet_addEthereumChain",
							params: [newNetworkConfig],
						});
						setTimeout(() => {
							sessionStorage.removeItem("token");
							location.reload();
						}, 100);
					} catch (error) {
						console.error("Error adding network to MetaMask:", error);
					}
				}
			} else {
				if (window.ethereum.chainId != 0x61) {
					// 要添加的网络信息
					const newNetworkConfig = {
						chainId: "0x61", // 以十六进制表示的链 ID
						chainName: "BNB Smart Chain Testnet",
						nativeCurrency: {
							name: "tBNB",
							symbol: "tBNB",
							decimals: 18,
						},
						rpcUrls: ["https://bsc-testnet.publicnode.com"],
						blockExplorerUrls: ["https://testnet.bscscan.com"],
					};
					try {
						// 添加新网络到 MetaMask
						await window.ethereum.request({
							method: "wallet_addEthereumChain",
							params: [newNetworkConfig],
						});
						setTimeout(() => {
							sessionStorage.removeItem("token");
							location.reload();
						}, 100);
					} catch (error) {
						console.error("Error adding network to MetaMask:", error);
					}
				}
			}
		},
		connectWallet() {
			$web3
				.connect_purse()
				.then((res) => {
					this.address = res;
					console.log(res);
				})
				.catch((err) => {
					console.log("connect_purse err------->", err);
				});
		},
		// 添加时间格式化方法
		formatTime(timestamp) {
			if (!timestamp) return ''
			const date = new Date(timestamp * 1000) // 如果后端返回的是秒级时间戳需要乘1000
			const year = date.getFullYear()
			const month = String(date.getMonth() + 1).padStart(2, '0')
			const day = String(date.getDate()).padStart(2, '0')
			const hours = String(date.getHours()).padStart(2, '0')
			const minutes = String(date.getMinutes()).padStart(2, '0')
			const seconds = String(date.getSeconds()).padStart(2, '0')

			return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
		},
		// 处理HTML标签
		stripHtml(html) {
			if (!html) return ''
			return html.replace(/<[^>]+>/g, '') // 去除所有 HTML 标签
				.replace(/\s{2,}/g, ' ') // 合并多个空格
				.trim()
		},
		// 格式化千分位数
		advancedFormat(value, options = {}) {
			if (value === undefined || value === null) return ''
			const { decimalPlaces = 2, decimalSeparator = '.', thousandsSeparator = ',' } = options
			const parts = value.toString().split('.')
			parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, thousandsSeparator)
			if (decimalPlaces > 0) {
				parts[1] = (parts[1] || '').slice(0, decimalPlaces)
				return parts.join(decimalSeparator)
			}
			return parts[0]
		},

		async refreshData() {
			this.isInitialized = false
			await this.initPageData()
		},
		// 获取区块链配置
		async getBlockchainConfig() {
			try {
				const res = await getBlockchainConfig()
				console.log("区块链配置:", res)
				if (res.data?.length) {
					const data = res.data[0]
					this.abi = data.token_abi
					this.contract_address = data.token_contract
					this.to_address = data.collect_address
				}
			} catch (error) {
				console.error('获取配置失败:', error)
			}
		},

	}
}
</script>

<style lang="scss" scoped>
.container {
	background-color: $bg-color;
	color: $text-color;
	// background-image: url('../../static/images/background.png');
	background-size: cover;
	background-position: center;
	min-height: 800rpx;
}

.banner-container {
	width: 100%;
	height: 360rpx;
	margin-bottom: 20rpx;
	overflow: hidden;
}

.home-background {
	background-image: url('../../static/background.png');
	background-size: cover;
	background-position: center;
	height: 100%;
	width: 100%;
}

.home {
	position: relative;
	z-index: 1;
	padding: 30rpx;
	color: #ffffff;
	/* background-color: #000000; */
	width: 100%;
	height: 100vh;
	overflow-y: auto;
	box-sizing: border-box;
	// padding-bottom: 50rpx;
	.home-box{
		display: flex;
		flex-direction: column;
		margin-top:373rpx;
		font-weight: Semi Bold;
		.home-title-box{
		display: flex;
		flex-direction: column;
			.select{
				color: #02F6F6;
			}
		.home-title{
			font-family: Inter, Inter;
			font-weight: normal;
			font-size: 45px;
			color: #FFFFFF;
			text-align: left;
			font-style: normal;
			text-transform: none;
		}
		.sub-title{
			font-family: Inter, Inter;
			font-weight: normal;
			font-size: 29px;
			color: #FFFFFF;
			text-align: left;
			font-style: normal;
			text-transform: none;
		}
	}
	.home-desc-box{
		display: flex;
		flex-direction: column;
		margin-top: 67rpx;
		.desc{
			font-family: PingFang SC, PingFang SC;
			font-weight: 500;
			font-size: 22px;
			color: #FFFFFF;
			text-align: left;
			font-style: normal;
			text-transform: none;
		}
		.sub-desc{
			font-family: Zen Dots, Zen Dots;
			font-weight: 400;
			font-size: 18px;
			color: #FFFFFF;
			text-align: left;
			font-style: normal;
			text-transform: none;
		}
	}
	.home-content{
		font-family: PingFang SC, PingFang SC;
		font-weight: 400;
		font-size: 14px;
		color: #FFFFFF;
		text-align: left;
		font-style: normal;
		text-transform: none;
		.home-content-title{
			margin-top: 44rpx;
			margin-bottom: 6rpx;
			display: flex;
			line-height: 20rpx;
			span{
				display: block;
				width: 8px;
				height: 8px;
				margin: 0 15rpx;
				border-radius: 50%;
				background: #FFFFFF;
			}
			
			
		}
	}

	}

}

.transparent-button {
	font-size: 16rpx;
	background-color: transparent;
	color: #ffffff;
	border: 1rpx solid #ffffff;
	cursor: pointer;
	margin: 0 20rpx;
}

.transparent-button:hover {
	background-color: rgba(255, 255, 255, 0.1);
}


.notification-bar {
	margin: 40rpx 0;
	width: 686rpx;
	height: 316rpx;
	background: #02142A;
	border-radius: 24rpx 24rpx 24rpx 24rpx;
	border: 2rpx solid #004676;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: space-evenly;

	.notification_dtext {
		width: 128rpx;
		height: 44rpx;
		font-family: PingFang SC, PingFang SC;
		font-weight: 400;
		font-size: 32rpx;
		color: #FFFFFF;
		line-height: 38rpx;
		text-align: left;
		font-style: normal;
		text-transform: none;
	}

	.notification_cfb {
		width: 548rpx;
		height: 168rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;

		.notification_cfb_tw {
			width: 114rpx;
			height: 168rpx;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: space-between;

			.notification_cfb_t {
				width: 114rpx;
				height: 114rpx;
			}

			.notification_cfb_w {
				width: 130rpx;
				height: 44rpx;
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				font-size: 32rpx;
				color: #FFFFFF;
				line-height: 38rpx;
				text-align: center;
				font-style: normal;
				text-transform: none;
			}
		}
	}
}

.jintiandontai {
	width: 686rpx;
	height: 496rpx;
	background: #02142A;
	border-radius: 24rpx 24rpx 24rpx 24rpx;
	border: 2rpx solid #004676;
	display: flex;
	flex-direction: column;
	align-items: center;

	.jintiandontai_tou {
		width: 128rpx;
		height: 44rpx;
		font-family: PingFang SC, PingFang SC;
		font-weight: 400;
		font-size: 32rpx;
		color: #FFFFFF;
		line-height: 38rpx;
		text-align: left;
		font-style: normal;
		text-transform: none;
		margin: 40rpx 0 24rpx 0;
	}

	.jintiandontai_neirong {
		width: 638rpx;
		height: 154rpx;
		background: #02142A;
		border-radius: 24rpx 24rpx 24rpx 24rpx;
		border: 2rpx solid #004676;
		display: flex;
		margin: 0 0 40rpx;

		.jintiandontai_tu {
			width: 50rpx;
			height: 50rpx;
			margin: 24rpx 18rpx 0 22rpx;
		}

		.jintiandontai_textzi {
			// width: 460rpx;
			height: 106rpx;
			font-family: PingFang SC, PingFang SC;
			font-weight: 400;
			font-size: 32rpx;
			color: #FFFFFF;
			line-height: 38rpx;
			text-align: left;
			font-style: normal;
			text-transform: none;
			display: flex;
			flex-direction: column;
			margin: 24rpx 0 0 0;

			.jintiandontai_time {
				margin: 18rpx 0 0;
				/* 新增多行省略核心代码 */
				// display: -webkit-box;
				// /* 弹性盒模型 */
				// -webkit-box-orient: vertical;
				// /* 垂直方向排列 */
				// -webkit-line-clamp: 1;
				// /* 显示行 (根据高度计算) */
				// overflow: hidden;
				// /* 溢出隐藏 */
				// text-overflow: ellipsis;
				/* 显示省略号 */
				.jintiandontai_timex{
					font-size: 28rpx;
				}
			}
		}

	}
}

.redian {
	margin: 40rpx 0;
	display: flex;
	align-items: center;
	justify-content: space-between;

	.hot_options_text {
		// width: 144rpx;
		height: 50rpx;
		font-family: PingFang SC, PingFang SC;
		font-weight: 600;
		font-size: 36rpx;
		color: #3E89FF;
		line-height: 42rpx;
		text-align: left;
		font-style: normal;
		text-transform: none;
	}

	.hot_options_dixian {
		width: 148rpx;
		height: 4rpx;
		background: linear-gradient(270deg, rgba(244, 224, 179, 0) 0%, #3E89FF 54%, rgba(235, 216, 177, 0) 100%);
		border-radius: 0rpx 0rpx 0rpx 0rpx;
	}

}

.redianimg {
	width: 50rpx;
	height: 50rpx;
	cursor: pointer;
}

.diao_box {}

.diao_list {
	margin: 24rpx 0 0 0;
	height: 44rpx;
	font-family: PingFang SC, PingFang SC;
	font-weight: 400;
	font-size: 32rpx;
	color: #FFFFFF;
	line-height: 38rpx;
	text-align: left;
	font-style: normal;
	text-transform: none;
}

.diao_lisx {
	margin: 16rpx 0 24rpx 0;
	// height: 40rpx;
	font-family: PingFang SC, PingFang SC;
	font-weight: 400;
	font-size: 28rpx;
	color: #828282;
	line-height: 33rpx;
	text-align: left;
	font-style: normal;
	text-transform: none;
}

.diao_hr {
	// width: 670rpx;
	height: 0rpx;
	border: 2rpx solid #004676;
	opacity: 0.6;
	margin: 0 10rpx;
}

.diao_di {
	// width: 560rpx;
	margin: 24rpx 0 0 0;
	height: 44rpx;
	font-family: PingFang SC, PingFang SC;
	font-weight: 400;
	font-size: 32rpx;
	color: #FFFFFF;
	line-height: 38rpx;
	text-align: left;
	font-style: normal;
	text-transform: none;
}



.trarentbutton {
	width: 416rpx;
	height: 40rpx;
	background-color: transparent;
	color: #e8c78e;
	border-radius: 10rpx;
	cursor: pointer;
	padding: 15rpx 10rpx;
	background-color: #000000;
	display: flex;
	align-items: center;
	margin: 0 0 20rpx 0;
}

.trarentbuttonda {
	font-size: 40rpx;
	font-weight: 600;
}


.trarentbutt {
	margin: 20rpx 15rpx 0;
	padding: 20rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	color: #000;
	background: linear-gradient(to right, #d39b53, #f8d498);
	/* border: 1rpx solid #302f2c; */
	border-radius: 10rpx;
}






.tuwenan {
	font-size: 14px;
	color: #828282;
}


.lunbotu {
	width: 686rpx;
	height: 320rpx;
	background: #02142A;
	border-radius: 24rpx 24rpx 24rpx 24rpx;
	border: 2rpx solid #004676;
	display: flex;
	align-items: center;
	justify-content: space-around;

	.lunbotuimg {
		width: 245rpx;
		height: 272rpx;
	}

	.lunbotuzi {
		width: 288rpx;
		height: 88rpx;
		font-family: PingFang SC, PingFang SC;
		font-weight: 400;
		font-size: 32rpx;
		color: #FFFFFF;
		line-height: 38rpx;
		text-align: left;
		font-style: normal;
		text-transform: none;
	}
}




.daxin {
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	margin: 0 0 0 20rpx;
}

.homeqian {
	display: flex;
	justify-content: space-between;
}




.liji {
	font-size: 17px;
	font-weight: 600;
}

.loading-tips {
	height: 80rpx;
	display: flex;
	justify-content: center;
	align-items: center;
	color: #999;
	font-size: 24rpx;
}
</style>