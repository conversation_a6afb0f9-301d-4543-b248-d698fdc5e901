<template>
	<view class="baodan-background">
		<!-- 顶部导航栏 -->
		<Header></Header>
		<view class="baodan">
			<view class="baodan_denji">
				<view class="baodan_denji_item">
					<img class="baodan_denji_img" src="../../static/ailkimg/dqdj.png" alt="">
					<view class="baodan_denji_dqdj">{{ $t('_Ailk.current_level') }} {{ userasset.level_name }}</view>
				</view>
				<view class="baodan_denji_item">
					<img class="baodan_denji_img" src="../../static/ailkimg/dqdj.png" alt="">
					<view class="baodan_denji_dqdj">{{ $t('_Ailk.referrer') }}{{ userasset.ref_user_address }}</view>
					<view class="baodan_denji_dqdjl">({{ $t('_Ailk.bound') }})</view>
				</view>
			</view>
			<view class="baodan_denjivip">
				<view class="baodan_denjivip_box">
					<img class="baodan_denjivip_img" src="../../static/ailkimg/dqdj.png" alt="">
					<view class="baodan_denjivip_dj">{{ baodanxq.name }}</view>
					<!-- <view class="baodan_denjivip_dj">{{ $t('_Ailk.next_level') }}{{ baodanxq.name }}</view> -->
				</view>
				<view class="baodan_denjivip_boxlddq">
					<view class="baodan_denjivip_bq">{{ $t('_Ailk.order_fee') }}</view>
					<view class="baodan_denjivip_ybq">
						<view class="baodan_denjivip_ybq1">{{
							(typeof baodanxq.price === 'number'
								? baodanxq.price
								: Number(baodanxq.price || 0)
							).toFixed(2)
						}} Ailk</view>
						<!-- <view class="baodan_denjivip_ybq2">(3%{{ $t('_Ailk.platform_bdfee') }})</view> -->
					</view>
				</view>
				<view class="baodan_denjivip_boxlddq">
					<view class="baodan_denjivip_bq">{{ $t('_Ailk.cross_pool_equity') }}</view>
					<view class="baodan_denjivip_ybq">
						<view class="baodan_denjivip_ybq1">+{{
							(typeof baodanxq.cross_pool === 'number'
								? baodanxq.cross_pool
								: Number(baodanxq.cross_pool || 0)
							).toFixed(2)
						}} U ≈ Ailk</view>
						<view class="baodan_denjivip_ybqfk" @click="toggleChuan"
							:class="{ 'active-checkbox': isChuan }">
							<text v-show="isChuan">✔️</text>
						</view>
						<view class="baodan_denjivip_ybq2">{{ $t('_Ailk.optional') }}</view>
					</view>
				</view>
				<view class="baodan_denjivip_boxlddq" v-if="baodanxq.has_bet_task !== 0">
					<view class="baodan_denjivip_bq">{{ $t('_Ailk.bet_pool_equity') }}</view>
					<view class="baodan_denjivip_ybq">
						<view class="baodan_denjivip_ybq1">+{{
							(typeof baodanxq.has_pool === 'number'
								? baodanxq.has_pool
								: Number(baodanxq.has_pool || 0)
							).toFixed(2)
						}}U ≈ Ailk</view>
						<view class="baodan_denjivip_ybqfk" @click="toggleCheck"
							:class="{ 'active-checkbox': isChecked }">
							<text v-show="isChecked">✔️</text>
						</view>
						<view class="baodan_denjivip_ybq2">{{ $t('_Ailk.optional') }}</view>
					</view>
				</view>
				<view class="baodan_denjivip_boxlddq">
					<view class="baodan_denjivip_bq">{{ $t('_Ailk.upgrade_receive') }}</view>
					<view class="baodan_denjivip_ybq">
						<view class="baodan_denjivip_ybq1">
							{{ $t('_Ailk.second_generation_reward', { level: baodanxq.level || 0 }) }}
						</view>
					</view>
				</view>
				<view class="baodan_denjivip_boxlddq" style="margin: 60rpx 40rpx 0;display: none;">
					<view class="baodan_denjivip_bq">{{ $t('_Ailk.no_bet_task') }}</view>
				</view>
				<view class="baodan_denjivip_boxlddq" style="margin: 24rpx 40rpx 60rpx;display:none;">
					<view class="baodan_denjivip_bq">{{ $t('_Ailk.activation_condition') }}</view>
					<view class="baodan_denjivip_ybq">
						<!-- <view class="baodan_denjivip_ybq1">20 Ailk</view> -->
						<view class="baodan_denjivip_ybq2">{{ $t('_Ailk.wallet_balance') }}</view>
					</view>
				</view>
				<view class="baodan_denjivip_button" @tap="tapqsubmit">
					<view class="baodan_denjivip_buttonqd">{{ $t('_Ailk.activate_now') }}</view>
				</view>
			</view>
			<view class="baodansomin">
				<view class="baodansomin_tefr">{{ $t('_Ailk.declaration_explanation') }}</view>
				<view class="baodansomin_cdcd">
					<!-- <view class="baodansomin_cdcdt">{{ baodanxq.remark }}</view> -->
					<view class="baodansomin_cdcdt" v-html="formatRemark(baodanxq.remark)"></view>
				</view>
				<!-- <view class="baodansomin_cdcd">
					<view class="baodansomin_cdcdy"></view>
					<view class="baodansomin_cdcdt">每级支付 20 Ailk，3%为平台手续费 </view>
				</view> -->
			</view>
		</view>
	</view>

</template>

<script setup>
import { ref, onMounted } from 'vue';
import Header from '../../components/common/header.vue';
import { qdetail, getMemberInfo, qsubmit } from '../../api/ailk.js';

const baodanxq = ref({});
const userasset = ref({});
const isChecked = ref(false);
const toggleCheck = () => {
	isChecked.value = !isChecked.value;
};
const isChuan = ref(false);
const toggleChuan = () => {
	isChuan.value = !isChuan.value;
};
const formatRemark = (text) => {
	return text.replace(/\r\n/g, '<br>').replace(/\n/g, '<br>');
};
// 组件挂载后执行
onMounted(() => {
	getqdetails();
	getquserInfos();
});
// 报单详情
const getqdetails = async () => {
	const params = {
	};
	let res = await qdetail(params)
	//   console.log("报单详情:", res);
	if (res?.code === 200) {
		baodanxq.value = res?.data;
		console.log("报单详情:", baodanxq.value);
	} else {
		console.log({ message: res?.msg || "数据获取失败", type: "fail" });
	}
};

// 获取用户资产
const getquserInfos = async () => {
	const params = {

	};
	let res = await getMemberInfo(params)
	// console.log("获取用户资产:", res);

	if (res?.code === 200) {
		userasset.value = res?.data;
		console.log("获取用户资产:", userasset.value);
	} else {
		console.log({ message: res?.msg || "数据获取失败", type: "fail" });
	}
};
// 激活报单
const tapqsubmit = async () => {
	// 点击前检查是否选中
	// if (!isChecked.value && !isChuan.value) {
	// 	handleFail("请选择选项");
	// 	return;
	// }
	const params = {
		product_id: baodanxq.value?.id || 0,
		// 判断是否选中，选中为1，未选中为0
		has_cross_pool: isChuan.value ? 1 : 0,
		has_pool: isChecked.value ? 1 : 0,
	};
	let res = await qsubmit(params)
	console.log("激活报单:", res);
	if (res?.code === 200) {
		handleSuccess(res?.msg || "激活成功");
		setTimeout(() => {
			uni.showToast({
				title: "激活成功",
				icon: "success",
				duration: 1000,
			});
		}, 1000);
		getquserInfos();
		getqdetails();
	} else {
		handleFail(res?.msg || "激活失败");
		setTimeout(() => {
			uni.showToast({
				title: res?.msg || "激活失败",
				icon: "error",
				duration: 2000,
			});
		}, 1000);
		getquserInfos();
		getqdetails();
	}
};

// 成功处理方法
const handleSuccess = (message) => {
	uni.showToast({
		title: message,
		icon: 'success',
		duration: 2000
	});
};
// 失败处理方法
const handleFail = (message) => {
	uni.showToast({
		title: message,
		icon: 'error',
		duration: 2000
	});
};
</script>

<style lang="scss" scoped>
.baodan-background {
	height: 100%;
	width: 100%;
}

.baodan {
	color: #ffffff;
	padding: 32rpx;
	height: 100%;
	width: 100%;
	box-sizing: border-box;

	.baodan_denji {
		// width: 686rpx;
		height: 196rpx;
		background: #02142A;
		border-radius: 24rpx;
		border: 2rpx solid #004676;
		margin: 0 0 40rpx 0;

		.baodan_denji_item {
			height: 44rpx;
			margin: 40rpx 0 0 40rpx;
			display: flex;
			align-items: center;

			.baodan_denji_img {
				width: 32rpx;
				height: 32rpx;
				margin: 0 0 4rpx 0;
			}

			.baodan_denji_dqdj {
				// width: 268rpx;
				margin: 0 0 0 24rpx;
				height: 44rpx;
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				font-size: 32rpx;
				color: #FFFFFF;
				line-height: 38rpx;
				text-align: left;
				font-style: normal;
				text-transform: none;
			}

			.baodan_denji_dqdjl {
				color: #71A5CE;
				height: 44rpx;
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				font-size: 32rpx;
				line-height: 38rpx;
				text-align: left;
				font-style: normal;
				text-transform: none;
			}
		}
	}

	.baodan_denjivip {
		// width: 686rpx;
		margin: 0 0 40rpx 0;
		// padding: 0 0 40rpx 0;
		// height: 586rpx;
		background: #02142A;
		border-radius: 24rpx 24rpx 24rpx 24rpx;
		border: 2rpx solid #004676;

		.baodan_denjivip_box {
			height: 44rpx;
			margin: 40rpx 0 0 40rpx;
			display: flex;
			align-items: center;

			.baodan_denjivip_img {
				width: 32rpx;
				height: 32rpx;
				margin: 0 0 4rpx 0;
			}

			.baodan_denjivip_dj {
				// width: 136rpx;
				margin: 0 0 0 24rpx;
				height: 44rpx;
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				font-size: 32rpx;
				color: #FFFFFF;
				line-height: 38rpx;
				text-align: left;
				font-style: normal;
				text-transform: none;
			}
		}

		.baodan_denjivip_boxlddq {
			height: 44rpx;
			margin: 24rpx 40rpx 0;
			display: flex;
			align-items: center;
			justify-content: space-between;

			.baodan_denjivip_bq {
				// width: 160rpx;
				// width: 40%;
				height: 44rpx;
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				font-size: 32rpx;
				color: #FFFFFF;
				line-height: 38rpx;
				text-align: left;
				font-style: normal;
				text-transform: none;
			}

			.baodan_denjivip_ybq {
				height: 44rpx;
				display: flex;
				align-items: center;

				.baodan_denjivip_ybq1 {
					// width: 104rpx;
					height: 44rpx;
					font-family: PingFang SC, PingFang SC;
					font-weight: 400;
					font-size: 32rpx;
					color: #FFFFFF;
					line-height: 38rpx;
					text-align: right;
					font-style: normal;
					text-transform: none;
				}

				.baodan_denjivip_ybq2 {
					// width: 204rpx;
					margin: 0 0 0 8rpx;
					height: 40rpx;
					font-family: PingFang SC, PingFang SC;
					font-weight: 400;
					font-size: 28rpx;
					color: #71A5CE;
					line-height: 33rpx;
					text-align: right;
					font-style: normal;
					text-transform: none;
				}

				.baodan_denjivip_ybqfk {
					width: 40rpx;
					height: 40rpx;
					border-radius: 8rpx;
					border: 2rpx solid #71A5CE;
					margin: 0 0 0 8rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					cursor: pointer;
				}

				.active-checkbox {
					border-color: #39EAFD;
					background: rgba(57, 234, 253, 0.1);
				}
			}
		}

		.baodan_denjivip_button {
			// width: 640rpx;
			margin: 60rpx 23rpx 46rpx;
			height: 70rpx;
			background: #3E89FF;
			border-radius: 8rpx 8rpx 8rpx 8rpx;
			display: flex;
			align-items: center;
			justify-content: center;

			.baodan_denjivip_buttonqd {
				// width: 120rpx;
				height: 30rpx;
				font-family: PingFang SC, PingFang SC;
				font-weight: 600;
				font-size: 30rpx;
				color: #FFFFFF;
				line-height: 30rpx;
				text-align: center;
				font-style: normal;
				text-transform: none;
			}
		}
	}

	.baodansomin {
		// width: 686rpx;
		// height: 242rpx;
		background: #02142A;
		border-radius: 24rpx;
		border: 2rpx solid #004676;
		padding: 40rpx 40rpx 40rpx 0;

		.baodansomin_tefr {
			margin: 0 0 24rpx 40rpx;
			height: 44rpx;
			font-family: PingFang SC, PingFang SC;
			font-weight: 400;
			font-size: 32rpx;
			color: #FFFFFF;
			line-height: 38rpx;
			text-align: left;
			font-style: normal;
			text-transform: none;
		}

		.baodansomin_cdcd {
			// height: 34rpx;
			display: flex;
			align-items: center;
			margin: 0 0 24rpx 40rpx;

			.baodansomin_cdcdy {
				width: 10rpx;
				height: 10rpx;
				background: #71A5CE;
				border-radius: 50%;
				margin: 0 10rpx 0 0;
			}

			.baodansomin_cdcdt {
				// width: 370rpx;
				// height: 34rpx;
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				font-size: 24rpx;
				color: #71A5CE;
				// line-height: 28rpx;
				// text-align: left;
				// font-style: normal;
				// text-transform: none;
			}
		}
	}
}
</style>
