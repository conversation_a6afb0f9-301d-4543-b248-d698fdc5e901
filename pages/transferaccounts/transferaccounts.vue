<template>
	<view class="transferaccounts">
		<!-- 顶部导航栏 -->
		<Header></Header>
		<view class="transferaccounts_content">

			<view class="trans_boxldbklddq">
				<view class="trans_boxldbklddqxh">
					<view class="trans_boxldbklddqla">
						<view class="trans_boxldbklddqla3">{{ $t('_Ailk.receiving_address') }}</view>
					</view>
					<view class="trans_boxldbkldTeImg" style="display: none;">
						<view class="trans_boxldbkldtext">{{ $t('_Ailk.choose_wallet') }}</view>
						<img class="trans_boxldbklddqimg" src="../../static/ailkimg/zhuanzhangyb.png" alt="">
					</view>

				</view>
				<view class="trans_boxldbklddqxh">
					<view class="trans_boxldbklddqla">
						<input class="trans_boxldbklddqla1" type="text" :placeholder="$t('_Ailk.enter_or_paste_wallet_address')" v-model="receivingaddress" />
					</view>
					<img class="trans_boxldbklddqimgsm" src="../../static/ailkimg/zhuanzhangdzsm.png" alt="">
				</view>

			</view>

			<view class="trans_boxldbklddq">
				<view class="trans_boxldbklddqxh">
					<view class="trans_boxldbklddqla">
						<view class="trans_boxldbklddqla3">{{ $t('_Ailk.transfer_amount') }}</view>
					</view>
					<view class="trans_boxldbkldTeImg">
						<view class="trans_boxldbkldtext">Ailk</view>
						<img class="trans_boxldbklddqimg" src="../../static/ailkimg/zhuanzhangyb.png" alt="">
					</view>

				</view>
				<view class="trans_boxldbklddqxh">
					<view class="trans_boxldbklddqla">
						<input class="trans_boxldbklddqla1" type="text" :placeholder="$t('_Ailk.please_enter_the_quantity')"  v-model="transferamount" />
					</view>
					<view class="trans_quanbu">
						<view class="trans_quanbutext">{{ $t('_Ailk.all') }}</view>
					</view>
				</view>
				<view class="trans_boxldbklddqxh">
					<view class="trans_boxldbklddqla">
						<view class="trans_boxldbklddqla3">{{ $t('_Ailk.wallet_zzbalance') }}</view>
					</view>
					<view class="trans_boxldbkldTeImg">
						<view class="trans_boxldbkldtext1">{{ userasset?.total_amount }} Ailk</view>
					</view>

				</view>

			</view>

			<view class="trans_button" @tap="postqtransfers">
				<view class="trans_buttonqd">{{ $t('_Ailk.confirm') }}</view>
			</view>

		</view>

	</view>
</template>

<script setup>
import Header from '../../components/common/header.vue';
import { ref, reactive, onMounted } from 'vue';
import { qtransfer,getMemberInfo, } from '../../api/ailk.js';

const receivingaddress = ref('') // 收款地址
const transferamount = ref('') // 转账金额
const userasset = ref({}); // 用户资产

// 组件挂载后执行
onMounted(() => {
	getquserInfos();
});
// 获取用户资产
const getquserInfos = async () => {
	const params = {

	};
	let res = await getMemberInfo(params)
	if (res?.code === 200) {
		userasset.value = res?.data;
		console.log("用户资产userasset:", userasset.value);
	} else {
		console.log({ message: res?.msg || "失败", type: "fail" });
	}
};
// 转账
const postqtransfers = async () => {

	// 参数校验逻辑
	if (!transferamount.value || isNaN(transferamount.value) || Number(transferamount.value) <= 0) {
		uni.showToast({
			title: `转账金额必须为大于0的数字`,
			icon: 'error',
			duration: 1000
		});
		return;
	}

	if (!receivingaddress.value?.trim()) {
		uni.showToast({
			title: '收款地址不能为空',
			icon: 'error',
			duration: 1000
		});
		return;
	}

	// 构造请求参数
	const params = {
		type: 1,
		amount: transferamount.value,
		address: receivingaddress.value,
	};

	try {
		const res = await qtransfer(params);
		if (res?.code === 200) {
			uni.showToast({
				title: '转账成功',
				icon: 'success',
				duration: 1000
			});
			// 可选：成功后重置表单
			transferamount.value = '';
			receivingaddress.value = '';
		} else {
			uni.showToast({
				title: res?.msg || '请求异常',
				icon: 'error',
				duration: 1000
			});
		}
	} catch (error) {
		console.error('请求失败:', error);
		uni.showToast({
			title: '网络或服务器错误',
			icon: 'error',
			duration: 1000
		});
	} 
};

</script>

<style lang="scss" scoped>
.transferaccounts {
	height: 100%;
	width: 100%;

	.transferaccounts_content {
		color: #ffffff;
		padding: 32rpx;
		height: 100%;
		width: 100%;
		box-sizing: border-box;

		.trans_boxldbklddq {
			margin: 0 0 40rpx;
			padding: 0 26rpx 40rpx;
			background: #02142A;
			border-radius: 24rpx 24rpx 24rpx 24rpx;
			border: 2rpx solid #004676;
			display: flex;
			flex-direction: column;

			.trans_boxldbklddqxh {
				margin: 28rpx 0 0;
				height: 44rpx;
				display: flex;
				align-items: center;
				justify-content: space-between;

				.trans_boxldbklddqla {
					height: 44rpx;
					display: flex;
					align-items: center;

					.trans_boxldbklddqla1 {
						height: 44rpx;
						font-family: PingFang SC, PingFang SC;
						font-weight: 400;
						font-size: 32rpx;
						color: #71A5CE;
						line-height: 38rpx;
						text-align: left;
						font-style: normal;
						text-transform: none;
					}
					

					.trans_boxldbklddqla2 {
						height: 44rpx;
						font-family: PingFang SC, PingFang SC;
						font-weight: 400;
						font-size: 32rpx;
						color: #39EAFD;
						line-height: 38rpx;
						text-align: left;
						font-style: normal;
						text-transform: none;
					}

					.trans_boxldbklddqla3 {
						// width: 128rpx;
						height: 44rpx;
						font-family: PingFang SC, PingFang SC;
						font-weight: 400;
						font-size: 32rpx;
						color: #FFFFFF;
						text-align: left;
						font-style: normal;
						text-transform: none;
					}
				}

				.trans_boxldbkldTeImg {
					height: 44rpx;
					display: flex;
					// justify-content: center;
					align-items: center;

					.trans_boxldbkldtext {
						font-family: PingFang SC, PingFang SC;
						font-weight: 400;
						font-size: 32rpx;
						color: #71A5CE;
						text-align: left;
						font-style: normal;
						text-transform: none;
						margin: 0 8rpx 3rpx 0;
					}

					.trans_boxldbkldtext1 {
						// width: 74rpx;
						// height: 34rpx;
						font-family: PingFang SC, PingFang SC;
						font-weight: 400;
						font-size: 24rpx;
						color: #FFFFFF;
						text-align: right;
						font-style: normal;
						text-transform: none;
					}

					.trans_boxldbklddqimg {
						width: 28rpx;
						height: 28rpx;
					}
				}

				.trans_boxldbklddqimgsm {
					width: 32rpx;
					height: 32rpx;
				}

				.trans_quanbu {
					width: 80rpx;
					height: 36rpx;
					border-radius: 198rpx 198rpx 198rpx 198rpx;
					border: 2rpx solid #71A5CE;
					display: flex;
					justify-content: center;
					align-items: center;


					.trans_quanbutext {
						font-family: PingFang SC, PingFang SC;
						font-weight: 400;
						font-size: 20rpx;
						color: #71A5CE;
						text-align: right;
						font-style: normal;
						text-transform: none;
					}
				}

			}



		}

		.trans_button {
			// width: 640rpx;
			margin: 80rpx 23rpx;
			height: 70rpx;
			background: #3E89FF;
			border-radius: 8rpx;
			display: flex;
			align-items: center;
			justify-content: center;

			.trans_buttonqd {
				// width: 120rpx;
				height: 30rpx;
				font-family: PingFang SC, PingFang SC;
				font-weight: 600;
				font-size: 30rpx;
				color: #FFFFFF;
				line-height: 30rpx;
				text-align: center;
				font-style: normal;
				text-transform: none;
			}
		}
	}
}
</style>
