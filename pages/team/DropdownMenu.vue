<template>
    <div class="dropdown-menu">
        <!-- 显示当前选中的值 -->
        <div class="dropdown-header" @click="toggleMenu">
            <span>{{ selectedOption.text || placeholder }}</span>
            <img class="dropdown-arrow" :class="{ open: isOpen }" src="../../static/images/dengjixiala.png"
                alt="arrow" />
        </div>

        <!-- 下拉选项列表 -->
        <ul v-if="isOpen" class="dropdown-options">
            <li v-for="option in options" :key="option.value" :class="{ active: option.value === modelValue }"
                @click="selectOption(option)">
                {{ option.text }}
            </li>
        </ul>
    </div>
</template>

<script setup>
import { ref, watch } from "vue";

// Props
defineProps({
    modelValue: String, // 当前选中的值
    options: {
        type: Array,
        required: true,
    },
    placeholder: {
        type: String,
        default: "请选择等级",
    },
});

// Emits
const emit = defineEmits(["update:modelValue"]);

// State
const isOpen = ref(false); // 控制下拉菜单的显示状态
const selectedOption = ref({}); // 当前选中的选项

// 监听 modelValue 的变化，更新选中的选项
watch(
    () => modelValue,
    (newValue) => {
        selectedOption.value = options.find((option) => option.value === newValue) || {};
    },
    { immediate: true }
);

// 切换下拉菜单的显示状态
const toggleMenu = () => {
    isOpen.value = !isOpen.value;
};

// 选择一个选项
const selectOption = (option) => {
    emit("update:modelValue", option.value); // 更新父组件的值
    selectedOption.value = option; // 更新当前选中的选项
    isOpen.value = false; // 关闭下拉菜单
};


// 重置选项
const resetOption = () => {
    emit("update:modelValue", ""); // 重置父组件的值为空
    selectedOption.value = {}; // 清空当前选中的选项
};

// 监听父组件的通知事件
defineExpose({
    resetOption, // 暴露 resetOption 方法供父组件调用
});
</script>
<style scoped>
.dropdown-menu {
    width: 428rpx;
    height: 52rpx;
    background: #0F0F0F;
    border-radius: 10rpx;
    position: relative;
    /* width: 100%; */
    /* max-width: 300rpx; */
    font-size: 24rpx;
    color: #fff;
    z-index: 3000;
    /* 确保下拉菜单显示在其他元素之上 */
}

.dropdown-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10rpx 15rpx;
    background-color: #0F0F0F;
    /* border: 1rpx solid #333; */
    border-radius: 5rpx;
    cursor: pointer;
}

.dropdown-header span {
    flex: 1;
}

.dropdown-arrow {
    width: 24rpx;
    height: 24rpx;
    transition: transform 0.3s;
}

.dropdown-arrow.open {
    transform: rotate(180deg);
}

.dropdown-options {
    position: absolute;
    top: 100%;
    left: 0;
    width: 100%;
    background-color: #0F0F0F;
    /* border: 1rpx solid #333; */
    border-radius: 5rpx;
    margin-top: 5rpx;
    z-index: 3000;
    list-style: none;
    padding: 0;
    /* max-height: 200rpx;
    overflow-y: auto; */
    max-height: none;
    /* 取消高度限制 */
    overflow: visible;
    /* 显示溢出的内容 */
}

.dropdown-options li {
    padding: 5rpx;
    cursor: pointer;
    transition: background-color 0.3s;
}

.dropdown-options li:hover {
    background-color: #333;
}

.dropdown-options li.active {
    background-color: #f7d396;
    color: #000;
}
</style>