<template>
	<view class="fenhon-background">
		<!-- 顶部导航栏 -->
		<Header></Header>
		<view class="fenhon">
			<view class="zhongfenhon">
				<view class="zhongfenhon_title">
					<text class="zhongfenhon_text">{{ $t('_Ailk.my_total_dividend') }}</text>
					<view class="zhongfenhon_shu">
						<text class="zhongfenhon_shus">{{fhteam?.my_total_dividend}}  Ailk</text>
						<text class="zhongfenhon_cd">{{ $t('_Ailk.time_travel_betting') }}</text>
					</view>
				</view>
				<view class="zhongfenhon_title">
					<text class="zhongfenhon_text">{{ $t('_Ailk.today_available') }}</text>
					<view class="zhongfenhon_shu">{{fhteam?.today_claimed}}</view>
				</view>
				<view class="zhongfenhon_button" @click="getAward">
					<view>{{ $t('_Ailk.claim') }}</view>
				</view>

			</view>
			<view class="fenhoufener">
				<view class="fenhoufener_tefr">{{ $t('_Ailk.my_hshares') }}</view>
				<view class="fenhoufener_cdcd">
					<view class="fenhoufener_cdcdk">
						<text class="fenhoufener_cdcdk1">{{ $t('_Ailk.cross_pool') }}</text>
						<text class="fenhoufener_cdcdk2">{{fhteam?.crossing_pool}}{{ $t('_Ailk.share') }}</text>
					</view>
					<view class="fenhoufener_cdcdk">
						<text class="fenhoufener_cdcdk1">{{ $t('_Ailk.bet_pool') }}</text>
						<text v-if="fhteam?.betting_pool>0" class="fenhoufener_cdcdk2">{{fhteam?.betting_pool}}{{ $t('_Ailk.share') }}({{ $t('_Ailk.unlock') }})</text>
						<text v-else class="fenhoufener_cdcdk2">{{fhteam?.betting_pool}}{{ $t('_Ailk.share') }}({{ $t('_Ailk.notunlocked') }})</text>
					</view>
				</view>
			</view>
			<view class="fenhouminxi">
				<view class="fenhouminxi_tefr">{{ $t('_Ailk.dividend_details') }}</view>
				<view class="fenhouminxi_cdcd">
					<view class="fenhouminxi_cdcdy"></view>
					<view class="fenhouminxi_cdcdt">{{ $t('_Ailk.cross_pool_rule') }} 2%</view>
				</view>
				<view class="fenhouminxi_cdcd">
					<view class="fenhouminxi_cdcdy"></view>
					<view class="fenhouminxi_cdcdt">{{ $t('_Ailk.bet_pool_rule') }} 2%</view>
				</view>
			</view>
			<view class="cyctrqy">
				<view class="cyctrqy_db">{{ $t('_Ailk.cross_pool_investment') }}</view>
				<view class="cyctrqy_ye">
					<view class="cyctrqy_ye_db">{{ $t('_Ailk.current_zbalance') }}</view>
					<view class="cyctrqy_ye_zb">
						<view class="cyctrqy_ye_zb1">{{ poolinfo?.cross_chain_pool }}</view>
						<view class="cyctrqy_ye_zb2"> Ailk</view>
					</view>
				</view>
				<view class="cyctrqy_ye">
					<view class="cyctrqy_ye_db">{{ $t('_Ailk.my_investment') }}</view>
					<view class="cyctrqy_ye_zb">
						<view class="cyctrqy_ye_zb1">{{ poolinfo?.my_pool }}</view>
						<view class="cyctrqy_ye_zb2">份(=</view>
						<view class="cyctrqy_ye_zb1">{{ poolinfo?.my_pool_total }}</view>
						<view class="cyctrqy_ye_zb2"> Ailk)</view>
					</view>
				</view>
				<view class="cyctrqy_ye">
					<view class="cyctrqy_ye_db">当前对赌池总额：</view>
					<view class="cyctrqy_ye_zb">
						<view class="cyctrqy_ye_zb1">{{ poolinfo?.cross_chain_pool }}</view>
						<view class="cyctrqy_ye_zb2">U ≈ Ailk</view>
					</view>
				</view>
				<!-- <input type="text" :placeholder="$t('_Ailk.enter_amount')" class="cyctrqyinput" v-model="inputMeno" /> -->
				<!-- <view class="cyctrqy_button" @click="getqinvests">
					<view class="cyctrqy_button_zb">
						<text class="cyctrqy_button_zb1">{{ $t('_Ailk.tinvest') }}</text>
						<text class="cyctrqy_button_zb2">{{ $t('_Ailk.platform_fee') }}</text>
					</view>
				</view> -->
			</view>
			<!-- <view class="choujianjilu">{{ $t('_Ailk.claim_records') }}</view>
			<view class="choujianjiluxhx"></view>
			<view class="choujianjilusjj">
				<view class="choujianjilusjj_db">
					<text>{{ $t('_Ailk.time') }}</text>
					<text>{{ $t('_Ailk.type') }}</text>
					<text>{{ $t('_Ailk.reward') }}</text>
				</view>
				<view class="choujianjilusjj_hr"></view>
				<view class="choujianjilusjj_xb">
					<text>2025.04.01</text>
					<text>{{ $t('_Ailk.cross_pool') }}</text>
					<text>+50 Ailk</text>
				</view>
				<view class="choujianjilusjj_xb">
					<text>2025.04.01</text>
					<text>{{ $t('_Ailk.bet_pool') }}</text>
					<text>+50 Ailk</text>
				</view>
			</view> -->
		</view>
	</view>
</template>
<script setup>
import { ref, reactive, onMounted } from 'vue';
import Header from '../../components/common/header.vue';
// import { showToast } from 'vant';
import { qinvest, qinfo,quserInfo,qbonus_info,gtodayPool } from "/api/ailk.js";

const inputMeno = ref('');
const tramout = ref('');
const zonamout = ref('');
const zonuser = ref('');
const fhteam = ref({});
const poolinfo = ref({});
const getAward = () => {
	uni.showToast({
		title: "暂无领取",
		icon: "none",
		duration: 2000,
	});
};

// 组件挂载后执行
onMounted(() => {
	getqinfos();
	getquserInfos();
	getqbonus_info();
	gtoday_pool();
});
// 格式化千分位数
const advancedFormat = (value, options = {}) => {
	if (value == null || value === "") return options.placeholder || "--";

	const num = Number(String(value).replace(/,/g, ''));
	if (isNaN(num)) return options.placeholder || "--";

	return num.toLocaleString('en-US', {
		minimumFractionDigits: options.decimals || 0,
		maximumFractionDigits: options.decimals ?? 2,
		style: options.currency ? 'currency' : undefined,
		currency: options.currency || undefined
	});
};

// 投入资金接口
const getqinvests = async () => {
	const params = {
		amount: inputMeno.value,
	};

	let res = await qinvest(params)
	console.log("投入资金接口:", res);

	if (res?.code === 200) {
		tramout.value = res?.data;
		console.log("tramout:", tramout.value);
		uni.showToast({
			title: "投入成功",
			icon: "success",
			duration: 2000,
		});
		inputMeno.value = "";
	} else {
		console.log({ message: res?.msg || "数据获取失败", type: "fail" });
	}
};
// 获取奖池信息
const getqinfos = async () => {
	const params = {

	};

	let res = await qinfo(params)
	console.log("获取奖池信息:", res);

	if (res?.code === 200) {
		zonamout.value = res?.data;
		console.log("zonamout:", zonamout.value);
	} else {
		console.log({ message: res?.msg || "数据获取失败", type: "fail" });
	}
};
// 获取分红信息
const getqbonus_info = async () => {
	const params = {

	};
	let res = await qbonus_info(params)
	// console.log("获取分红信息:", res);
	if (res?.code === 200) {
		fhteam.value = res?.data;
		console.log("获取分红信息:", fhteam.value);
	} else {
		console.log({ message: res?.msg || "数据获取失败", type: "fail" });
	}
};

// 获取穿越池信息
const gtoday_pool = async () => {
	const params = {

	};
	let res = await gtodayPool(params)
	// console.log("获取分红信息:", res);
	if (res?.code === 200) {
		poolinfo.value = res?.data;
		console.log("获取穿越池信息:", poolinfo.value);
	} else {
		console.log({ message: res?.msg || "数据获取失败", type: "fail" });
	}
};
// 获取用户奖池持有信息接口
const getquserInfos = async () => {
	const params = {

	};

	let res = await quserInfo(params)
	console.log("获取用户奖池:", res);

	if (res?.code === 200) {
		zonuser.value = res?.data;
		console.log("zonuser:", zonuser.value);
	} else {
		console.log({ message: res?.msg || "数据获取失败", type: "fail" });
	}
};

</script>

<style lang="scss" scoped>
.fenhon-background {
	height: 100%;
	width: 100%;
}

.fenhon {
	position: relative;
	z-index: 1;
	padding: 30rpx;
	color: #ffffff;
	background-color: #000C1F;

	height: 100%;
	width: 100%;
	overflow-y: auto;
	box-sizing: border-box;

	.zhongfenhon {
		// width: 686rpx;
		height: 290rpx;
		background: #02142A;
		border-radius: 24rpx 24rpx 24rpx 24rpx;
		border: 2rpx solid #004676;
		padding: 12rpx 0 0 0;
		box-sizing: border-box;

		.zhongfenhon_title {
			height: 44rpx;
			margin: 28rpx 40rpx 0 40rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;


			.zhongfenhon_text {
				// width: 192rpx;
				height: 44rpx;
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				font-size: 32rpx;
				color: #FFFFFF;
				line-height: 38rpx;
				text-align: left;
				font-style: normal;
				text-transform: none;
			}

			.zhongfenhon_shu {
				height: 44rpx;
				display: flex;
				align-items: center;

				.zhongfenhon_shus {
					// width: 182rpx;
					height: 44rpx;
					font-family: PingFang SC, PingFang SC;
					font-weight: 400;
					font-size: 32rpx;
					color: #FFFFFF;
					line-height: 38rpx;
					text-align: right;
					font-style: normal;
					text-transform: none;
				}

				.zhongfenhon_cd {
					// width: 128rpx;
					margin: 0 0 0 8rpx;
					height: 34rpx;
					font-family: PingFang SC, PingFang SC;
					font-weight: 400;
					font-size: 24rpx;
					color: #71A5CE;
					line-height: 28rpx;
					text-align: left;
					font-style: normal;
					text-transform: none;
				}
			}
		}

		.zhongfenhon_button {
			// width: 638rpx;
			margin: 28rpx 24rpx 0;
			height: 66rpx;
			background: #3E89FF;
			border-radius: 8rpx 8rpx 8rpx 8rpx;
			display: flex;
			align-items: center;
			justify-content: center;
		}
	}

	.fenhoufener {
		// width: 686rpx;
		margin: 40rpx 0;
		height: 240rpx;
		background: #02142A;
		border-radius: 24rpx 24rpx 24rpx 24rpx;
		border: 2rpx solid #004676;

		.fenhoufener_tefr {
			// width: 192rpx;
			margin: 42rpx 0 24rpx 40rpx;
			height: 44rpx;
			font-family: PingFang SC, PingFang SC;
			font-weight: 400;
			font-size: 32rpx;
			color: #FFFFFF;
			line-height: 38rpx;
			text-align: left;
			font-style: normal;
			text-transform: none;
		}

		.fenhoufener_cdcd {
			height: 88rpx;
			display: flex;
			align-items: center;
			justify-content: space-evenly;

			.fenhoufener_cdcdk {
				height: 88rpx;
				display: flex;
				flex-direction: column;
				align-items: center;

				.fenhoufener_cdcdk1 {
					height: 44rpx;
					font-family: PingFang SC, PingFang SC;
					font-weight: 400;
					font-size: 32rpx;
					color: #FFFFFF;
					line-height: 38rpx;
					text-align: left;
					font-style: normal;
					text-transform: none;
				}

				.fenhoufener_cdcdk2 {
					height: 44rpx;
					font-family: PingFang SC, PingFang SC;
					font-weight: 400;
					font-size: 32rpx;
					color: #3E89FF;
					line-height: 38rpx;
					text-align: left;
					font-style: normal;
					text-transform: none;
				}
			}
		}
	}

	.fenhouminxi {
		// width: 686rpx;
		height: 242rpx;
		background: #02142A;
		border-radius: 24rpx 24rpx 24rpx 24rpx;
		border: 2rpx solid #004676;

		.fenhouminxi_tefr {
			margin: 42rpx 0 24rpx 40rpx;
			height: 44rpx;
			font-family: PingFang SC, PingFang SC;
			font-weight: 400;
			font-size: 32rpx;
			color: #FFFFFF;
			line-height: 38rpx;
			text-align: left;
			font-style: normal;
			text-transform: none;
		}

		.fenhouminxi_cdcd {
			height: 34rpx;
			display: flex;
			align-items: center;
			margin: 0 0 24rpx 40rpx;

			.fenhouminxi_cdcdy {
				width: 10rpx;
				height: 10rpx;
				background: #71A5CE;
				border-radius: 50%;
				margin: 0 10rpx 0 0;
			}

			.fenhouminxi_cdcdt {
				// width: 370rpx;
				height: 34rpx;
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				font-size: 24rpx;
				color: #71A5CE;
				line-height: 28rpx;
				text-align: left;
				font-style: normal;
				text-transform: none;
			}
		}
	}

	.cyctrqy {
		// width: 686rpx;
		margin: 40rpx 0;
		height: 100%;
		background: #02142A;
		border-radius: 24rpx;
		border: 2rpx solid #004676;

		.cyctrqy_db {
			// width: 224rpx;
			margin: 42rpx 0 24rpx 42rpx;
			height: 44rpx;
			font-family: PingFang SC, PingFang SC;
			font-weight: 400;
			font-size: 32rpx;
			color: #FFFFFF;
			line-height: 38rpx;
			text-align: left;
			font-style: normal;
			text-transform: none;
		}

		.cyctrqy_ye {
			height: 44rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
			margin: 0 40rpx 24rpx;


			.cyctrqy_ye_db {
				// width: 224rpx;
				height: 44rpx;
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				font-size: 32rpx;
				color: #FFFFFF;
				line-height: 38rpx;
				text-align: left;
				font-style: normal;
				text-transform: none;
			}

			.cyctrqy_ye_zb {
				// width: 162rpx;
				height: 44rpx;
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				font-size: 32rpx;
				color: #FFFFFF;
				line-height: 38rpx;
				text-align: left;
				font-style: normal;
				text-transform: none;
				display: flex;

				.cyctrqy_ye_zb1 {
					margin: 0 10rpx 0 0;
				}

				.cyctrqy_ye_zb2 {
					color: #71A5CE;
				}
			}
		}

		.cyctrqyinput {
			// width: 638rpx;
			margin: 8rpx 24rpx 0;
			height: 92rpx;
			background: #02142A;
			border-radius: 24rpx 24rpx 24rpx 24rpx;
			border: 2rpx solid #004676;
			padding: 0 26rpx;
		}

		.cyctrqy_button {
			// width: 640rpx;
			margin: 24rpx 23rpx;
			height: 104rpx;
			background: #3E89FF;
			border-radius: 8rpx 8rpx 8rpx 8rpx;
			display: flex;
			align-items: center;
			justify-content: center;

			.cyctrqy_button_zb {
				display: flex;
				flex-direction: column;
				align-items: center;

				.cyctrqy_button_zb1 {
					height: 30rpx;
					font-family: PingFang SC, PingFang SC;
					font-weight: 600;
					font-size: 30rpx;
					color: #FFFFFF;
					line-height: 30rpx;
					text-align: center;
					font-style: normal;
					text-transform: none;
				}

				.cyctrqy_button_zb2 {
					height: 30rpx;
					font-family: PingFang SC, PingFang SC;
					font-weight: 400;
					font-size: 24rpx;
					color: #FFFFFF;
					line-height: 30rpx;
					text-align: center;
					font-style: normal;
					text-transform: none;
				}
			}
		}
	}

	.choujianjilu {
		// width: 216rpx;
		height: 50rpx;
		font-family: PingFang SC, PingFang SC;
		font-weight: 600;
		font-size: 36rpx;
		color: #3E89FF;
		line-height: 42rpx;
		text-align: left;
		font-style: normal;
		text-transform: none;
	}

	.choujianjiluxhx {
		width: 216rpx;
		height: 4rpx;
		background: linear-gradient(270deg, rgba(244, 224, 179, 0) 0%, #3E89FF 54%, rgba(235, 216, 177, 0) 100%);
		border-radius: 0rpx 0rpx 0rpx 0rpx;
	}

	.choujianjilusjj {
		// width: 686rpx;
		margin: 40rpx 0 0 0;
		height: 640rpx;
		background: #02142A;
		border-radius: 8rpx 8rpx 8rpx 8rpx;
		border: 2rpx solid #004676;

		.choujianjilusjj_db {
			// width: 60rpx;
			height: 42rpx;
			font-family: PingFang SC, PingFang SC;
			font-weight: 400;
			font-size: 30rpx;
			color: #3E89FF;
			line-height: 35rpx;
			text-align: center;
			font-style: normal;
			text-transform: none;
			display: flex;
			align-items: center;
			justify-content: space-around;
			padding: 30rpx 0 0;
		}

		.choujianjilusjj_hr {
			width: 604rpx;
			height: 0rpx;
			border: 2rpx solid #004676;
			opacity: 0.6;
			margin: 28rpx 0 50rpx 40rpx;
		}

		.choujianjilusjj_xb {
			// width: 134rpx;
			height: 36rpx;
			font-family: PingFang HK, PingFang HK;
			font-weight: 500;
			font-size: 26rpx;
			color: #FFFFFF;
			line-height: 30rpx;
			text-align: center;
			font-style: normal;
			text-transform: none;
			display: flex;
			align-items: center;
			justify-content: space-around;
			margin: 0 0 42rpx 0;
		}
	}
}
</style>