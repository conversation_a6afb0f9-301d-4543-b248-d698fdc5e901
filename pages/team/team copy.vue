<template>
	<view class="home-background">
		<!-- 顶部导航栏 -->
		<Header></Header>
		<view class="home">
			<!-- <view class="homeqian">
				<button class="transparent-button" @click="connectWallet">连接钱包</button>
				<button class="transparent-button">CN</button>
			</view> -->
			<view class="trarent">
				<view class="juzhon">
					<view class="reimgda">
						<view class="reimgdashu">
							{{ user_info.ref_user_total }}
						</view>
						<view class="reimgdatext">
								{{ $t('mine.total_team_members') }}
						</view>
					</view>
				</view>
			</view>
			<view class="juzhon">
				<view class="youxiao">
					<view class="reimgdashu">
						{{ user_info.ref_user_direct }}
					</view>
					<view class="reimgdatext">
						{{ $t('mine.effective_team_members') }}
					</view>
				</view>
			</view>

			<view class="zhondui">
				<view class="zonzhen">
					<view class="reimgdatext">
						{{ $t('mine.total_orders_this_month') }}
					</view>
					<view class="zhenxiao">
						{{ user_info.order_volume }}
					</view>

				</view>
				<view class="zonzhen">
					<view class="reimgdatext">
						{{ $t('mine.new_orders_this_month') }}
					</view>
					<view class="zhenxiao">
						{{ user_info.order_added }}
					</view>
				</view>
			</view>

			<view class="rujing">
				<view class="rujingliang">
					<view class="rrjingtext">
						{{ $t('mine.deposit_amount_this_month') }}：{{ user_info.order_added_money }}
					</view>
					<view class="rujingdu">
						<view class="rujingbai">
							{{ $t('mine.mom') }}
						</view>
						<img class="rujingdutiao" src="../../static/images/Rectangle.png" alt="" />
						<view class="rujingbai">
							{{ teamIncomeRate }}%
						</view>
					</view>
				</view>
			</view>
			<view class="zhondui">
				<view class="zhituan">
					{{ $t('mine.direct_referral') }}({{ user_info.ref_user_direct }})
					<view class="zhonduidd">
						{{ $t('mine.team') }}({{ user_info.ref_user_total }})
					</view>
				</view>

				<view class="shixuan">
					{{ $t('mine.filter') }}
					<img class="shaimg" src="../../static/images/Frames.png" alt="" />
				</view>
			</view>
			<hr class="small-hr" />
			<view class="zhituizhanhao">
				<view class="zhituizhanminden">
					<text class="zhituanminchen">名称</text>
					<view class="zhituidenji">
						<text>等级</text>
						<img class="zhituidenjiimg" src="../../static/images/dengjixiala.png" alt="" />
					</view>
				</view>
				<view class="zhituizhanhaodenji" v-for="(row, index) in zhangHao" :key="index">
					<text class="zhituiminchen">{{ row.text }}</text>
					<view class="zhituiminchentext" @click="showPopup(row)">
						<text>{{ row.denji }}</text>
						<img class="zhituihuiyunimg" src="../../static/images/dengji.png" alt="" />
					</view>
				</view>
			</view>
			<!-- Vant Popup 弹出层 -->
			<van-popup :show="popupVisible" position="center" class="custom-popup" @close="popupVisible = false">
				<view class="popup-content">
					<view class="popup-title">等级修改</view>
					<view class="popup-user">用户：{{ selectedUser.text }}</view>
					<view class="popup-level">
						<text>等级</text>
						<!-- 使用自定义下拉菜单组件 -->
						<DropdownMenu v-model="selectedLevel" :options="levelOptions" placeholder="请选择等级" />
					</view>
					<van-button type="primary" class="confirm-button" @click="confirmChange">确定</van-button>
				</view>
			</van-popup>
		</view>
	</view>
</template>

<script>
import Header from '../../components/common/header.vue'
import { Toast } from 'vant';
import DropdownMenu from "./DropdownMenu.vue"; // 引入自定义下拉菜单组件
 import { getMemberInfo } from '../../api/api'
export default {
    components: {
        DropdownMenu,
		Header
    },
    data() {
        return {
            zhangHao: [
                { text: "1633****9564", denji: "T2" },
                { text: "1638****9566", denji: "T6" },
                { text: "1638****9569", denji: "T4" },
            ],
            popupVisible: false, // 控制弹出层显示
            selectedUser: {}, // 当前选中的用户
            selectedLevel: "T1", // 当前选中的等级
            levelOptions: [
                { text: "T1", value: "T1" },
                { text: "T2", value: "T2" },
                { text: "T3", value: "T3" },
                { text: "T4", value: "T4" },
                { text: "T5", value: "T5" },
            ],
            teamTotalNumber: 128,
            teamValidNumber: 0,
            teamTotalOrders: 0.00,
            teamNewOrders: 0.00,
            teamIncome: 0.00,
            teamIncomeRate: 0.00,
			user_info:{
				ref_user_total:0,
				ref_user_direct:0,
			}
        };
    },
	mounted() {
        	this.getMemberInfo();
    	},
    methods: {
        connectWallet() {
            // 这里可以添加连接钱包的逻辑
        },
        showPopup(row) {
            this.selectedUser = row;
            this.selectedLevel = row.denji;
            this.popupVisible = true;
        },
        confirmChange() {
            Toast(`等级已修改为：${this.selectedLevel}`);
            this.popupVisible = false;
        },
		async getMemberInfo() {
				const res = await getMemberInfo()
				console.log(res);
				this.user_info = res.data
				this.user_info.address = this.user_info.address.slice(0, 3) + '****' + this.user_info.address.slice(-3)
      		},
    },
    mounted() {
        // 可以在这里添加组件挂载后的逻辑
    },
};
</script>

<style lang="scss" scoped>
.home-background {
	background-image: url('../../static/images/background.png');
	background-size: cover;
	background-position: center;
	height: 100%;
	width: 100%;
}

.home {
	position: relative;
	z-index: 1;
	padding: 20rpx;
	color: #ffffff;
	// min-height: 100vh;
}

.homeqian {
	display: flex;
	justify-content: space-between;
}

.transparent-button {
	font-size: 16rpx;
	background-color: transparent;
	color: #ffffff;
	border: 1rpx solid #ffffff;
	cursor: pointer;
	margin: 0 20rpx;
}

.transparent-button:hover {
	background-color: rgba(255, 255, 255, 0.1);
}

.juzhon {
	display: flex;
	justify-content: center;
	align-items: center;
}

.trarent {
	display: flex;
	flex-direction: column;
	padding: 12rpx 0;
	/* margin: 20rpx 0 0 0; */
}

.reimgda {
	width: 688rpx;
	height: 504rpx;

	background-image: url('../../static/images/zongquan.png');
	background-size: cover;
	background-position: center;
	display: flex;

	align-items: center;
	justify-content: center;
	flex-direction: column;
}

.reimgdashu {
	color: #F7D396;
	font-size: 68rpx;
	font-weight: bold;
}

.reimgdatext {
	color: #ffffff;
	font-size: 36rpx;
}

.youxiao {
	margin: 30rpx 0 0 0;
	width: 660rpx;
	height: 214rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	flex-direction: column;
	background: linear-gradient(to top, #2d2d2d, #1a1a1a);
	border: 4rpx solid #302f2c;
	border-radius: 10rpx;
}

.zhondui {
	margin: 40rpx 0 0 0;
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.zonzhen {
	box-sizing: border-box;
	margin: 0 10rpx;
	width: 324rpx;
	height: 172rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	flex-direction: column;
	background: linear-gradient(to top, #2d2d2d, #1a1a1a);
	border: 4rpx solid #302f2c;
	border-radius: 10rpx;
}

.zhenxiao {
	color: #F7D396;
	font-size: 36rpx;
	font-weight: bold;
}

.rujing {
	margin: 40rpx 0 0 0;
	display: flex;
	justify-content: space-between;
	flex-direction: column;
}

.rujingliang {
	box-sizing: border-box;
	margin: 0 10rpx;
	width: 684rpx;
	height: 176rpx;
	display: flex;
	justify-content: center;
	flex-direction: column;
	padding: 36rpx;
	background: linear-gradient(to top, #2d2d2d, #1a1a1a);
	border: 4rpx solid #302f2c;
	border-radius: 10rpx;
}

.rujingbai {
	font-size: 28rpx;
}

.rujingdu {
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.rujingdutiao {
	width: 462rpx;
	height: 28rpx;
}

.rrjingtext {
	font-size: 36rpx;
	margin: 10rpx 0rpx;
}

.zhituan {
	display: flex;
	color: #F7D396;
	font-size: 33rpx;

}

.zhonduidd {
	font-size: 36rpx;
	color: #ffffff;
	margin: 0 0 0 24rpx;
}

.shixuan {
	display: flex;
	font-size: 32rpx;
}

.shaimg {
	width: 44rpx;
	height: 44rpx;
}

.small-hr {
	width: 95%;
	margin: 30rpx 13rpx;
	transform: scale(1, 0.2);
	background-color: #707070;
}

.hezhikun {
	/* width: 750rpx; */
	display: flex;
	justify-content: center;
	align-items: center;
}

.hezhi {
	width: 110.4rpx;
	height: 100%;
}

.zhituizhanhao {
	margin: 0 0 300rpx 0;
	height: 100%;
	.zhituizhanminden {
		display: flex;
		align-items: center;
		justify-content: space-between;
		font-size: 24rpx;
		color: rgba(255, 255, 255, 0.51);

		.zhituanminchen {
			margin: 0 0 0 94rpx;
		}

		.zhituidenji {
			display: flex;
			align-items: center;
			margin: 0 20rpx 0 0;

			.zhituidenjiimg {
				width: 32rpx;
				height: 32rpx;
				margin: 0 0 0 12rpx;
			}
		}
	}

	.zhituizhanhaodenji {
		display: flex;
		align-items: center;
		justify-content: space-between;
		font-size: 32rpx;
		margin: 20rpx 0;

		.zhituiminchen {
			margin: 0 0 0 16rpx;
		}

		.zhituiminchentext {
			display: flex;
			align-items: center;
			margin: 0 20rpx 0 0;

			.zhituihuiyunimg {
				width: 32rpx;
				height: 32rpx;
				margin: 0 0 0 12rpx;
			}
		}
	}
}

/* 弹出层样式 */
.custom-popup {
	padding: 32rpx 58rpx 66rpx;
	width: 630rpx;
	background: #272727;
	border-radius: 20rpx;
	border: 2rpx solid rgba(183, 183, 183, 0.41);
}

.van-popup::-webkit-scrollbar {
	display: none;
}

.popup-content {
	display: flex;
	flex-direction: column;
	align-items: center;
	color: #fff;
}

.popup-title {
	font-size: 36rpx;
	font-weight: bold;
	margin-bottom: 20rpx;
}

.popup-user {
	font-size: 28rpx;
	margin-bottom: 40rpx;
	color: #727272;
}

.popup-level {
	display: flex;
	align-items: center;
	justify-content: space-between;
	width: 100%;
	margin-bottom: 40rpx;
	font-size: 32rpx;
}

.popup-level text {
	color: #fff;
}

.confirm-button {
	width: 412rpx;
	height: 84rpx;
	background: linear-gradient(90deg, #D49D55 0%, #F7D396 100%);
	border-radius: 92rpx;
	color: #2E2E2E;
	border: 1rpx rgba(183, 183, 183, 0.41);
}
</style>
