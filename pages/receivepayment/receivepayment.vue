<template>
	<view class="receivepayment">
		<!-- 顶部导航栏 -->
		<Header></Header>
		<view class="receivepayment_content">
			<view class="receivepayment_erweima">
				<view class="receivepayment_erweima_gog">{{ $t('_Ailk.only_transfer_assets_to_this_address') }}</view>

				<view class="receivepayment_erweima_img">
					<ayQrcode ref="qrcode" :modal="modal_qr" :url="url" @hideQrcode="hideQrcode" :height="212"
						:width="212" />
				</view>

				<view class="receivepayment_erweima_textdizhi">
					<view class="receivepayment_erweima_textdizhi1">
						<vier class="receivepayment_erweima_textdizhit">{{ $t('_Ailk.receiving_address') }}</vier>
						<img class="receivepayment_erweima_textdiimg" src="../../static/ailkimg/shoukuandizi.png" alt="" @tap="copyText(userasset?.address)">
					</view>
					<view class="receivepayment_erweima_textdizhi2">{{ userasset?.address }}</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import Header from '../../components/common/header.vue';
import ayQrcode from "@/components/ay-qrcode/ay-qrcode.vue";
import { getMemberInfo, } from '../../api/ailk.js';
export default {
	components: {
		Header,
		ayQrcode,// 引入二维码组件

	},
	data() {
		return {
			userasset: {},
			//二维码相关参数
			modal_qr: false,
			// url: 'https://ailkapi.tocoinweb3.com/h5/index.html#/?invite_code=QBJ9NE13', // 要生成的二维码值
			url: `https://ailkapi.tocoinweb3.com/h5/index.html#/${this.userasset?.invite_code_url}`,

		}
	},

	onLoad() {
		let that = this;
		// 先执行异步请求，完成后生成二维码
		that.getquserInfos().then(() => {
			that.showQrcode();
		});
	},
	methods: {

		// 展示二维码
		showQrcode() {
			let _this = this;
			this.modal_qr = true;
			// uni.showLoading()
			setTimeout(function () {
				// uni.hideLoading()
				_this.$refs.qrcode.crtQrCode()
			}, 50)
		},

		//传入组件的方法
		hideQrcode() {
			this.modal_qr = false;
		},
		//  复制文本
		copyText(text) {
			uni.setClipboardData({
				data: text,
				success: function () {
					uni.showToast({
						title: '复制成功',
						icon: 'success',
						duration: 2000
					});
				}
			});
		},
		// 获取用户资产
		async getquserInfos() {
			let params = {};
			let res = await getMemberInfo(params)
			if (res?.code === 200) {
				this.userasset = res?.data;
				console.log("获取用户资产:", this.userasset); // 注意这里要加this
			} else {
				console.log({ message: res?.msg || "数据获取失败", type: "fail" });
			}
		},
	}

}
</script>

<style lang="scss" scoped>
.receivepayment {
	height: 100%;
	width: 100%;

	.receivepayment_content {
		color: #ffffff;
		padding: 32rpx;
		height: 100%;
		width: 100%;
		box-sizing: border-box;

		.receivepayment_erweima {
			height: 948rpx;
			background: #FAFAFA;
			border-radius: 24rpx 24rpx 24rpx 24rpx;
			border: 2rpx solid #004676;
			display: flex;
			flex-direction: column;
			align-items: center;

			.receivepayment_erweima_gog {
				height: 44rpx;
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				font-size: 32rpx;
				color: #000000;
				text-align: left;
				font-style: normal;
				text-transform: none;
				margin: 50rpx 0 54rpx;
			}

			.receivepayment_erweima_img {
				width: 424rpx;
				height: 424rpx;
				// background: #F0F1F2;
				margin: 0 0 60rpx;
			}

			.receivepayment_erweima_textdizhi {
				width: 536rpx;
				height: 201rpx;
				background: #F0F1F2;
				border-radius: 20rpx 20rpx 20rpx 20rpx;

				.receivepayment_erweima_textdizhi1 {
					height: 44rpx;
					margin: 28rpx 0 12rpx;
					display: flex;
					align-items: center;
					justify-content: center;

					.receivepayment_erweima_textdizhit {
						font-family: PingFang SC, PingFang SC;
						font-weight: 400;
						font-size: 32rpx;
						color: #757575;
						text-align: left;
						font-style: normal;
						text-transform: none;
					}

					.receivepayment_erweima_textdiimg {
						width: 28rpx;
						height: 28rpx;
						margin: 0 0 0 14rpx;
					}
				}

				.receivepayment_erweima_textdizhi2 {
					width: 100%; // 占满父容器宽度
					min-height: 88rpx; // 改用最小高度
					padding: 0 20rpx; // 增加左右内边距
					box-sizing: border-box; // 包含padding计算
					font-family: PingFang SC, PingFang SC;
					font-weight: 400;
					font-size: 32rpx;
					color: #000000;
					line-height: 1.5; // 增加行高
					word-break: break-all; // 允许任意位置换行
					white-space: pre-wrap; // 保留空白符但自动换行
					display: flex;
					align-items: center;
					justify-content: center;
					text-align: center;
				}
			}
		}
	}
}
</style>
