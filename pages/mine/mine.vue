<template>

  <view class="me_profile">
    <!-- 顶部导航栏 -->
    <Header></Header>
    <view class="user_profile">
      <view class="user_zhonglan">
        <view class="user_zhonglan_top">{{ $t('_Ailk.asset_overview') }}</view>
        <view class="user_zhonglan_zhon">
          <view class="user_zhonglan_zhon_left_top">{{ $t('_Ailk.total_assets') }}</view>
          <view class="user_zhonglan_zhon_left_bottom">{{ userasset.total_amount }} Ailk</view>
        </view>
        <view class="user_zhonglan_zhon">
          <view class="user_zhonglan_zhon_left_top">Ailk{{ $t('_Ailk.ailk_balance') }}</view>
          <view class="user_zhonglan_zhon_left_bottom">{{ userasset.amount }}</view>
        </view>
        <view class="user_zhonglan_zhon">
          <view class="user_zhonglan_zhon_left_top">{{ $t('_Ailk.consumption_account') }}</view>
          <view class="user_zhonglan_zhon_left_bottom">{{ userasset.score }}</view>
        </view>

        <view class="user_zhonglan_zhonszct">
          <view class="user_zhonglan_zhonszctf" @click="receivepayment">
            <img class="user_zhonglan_zhonszctfimg" src="../../static/ailkimg/shoukuan.png" alt="">
            <view class="user_zhonglan_zhonszctf1">{{ $t('_Ailk.receive_payment') }}</view>
          </view>
          <view class="user_zhonglan_zhonszctfhr"></view>
          <view class="user_zhonglan_zhonszctf" @click="transferaccounts">
            <img class="user_zhonglan_zhonszctfimg" src="../../static/ailkimg/zhuanzhang.png" alt="">
            <view class="user_zhonglan_zhonszctf1">{{ $t('_Ailk.transfer_accounts') }}</view>
          </view>
          <view class="user_zhonglan_zhonszctfhr"></view>
          <view class="user_zhonglan_zhonszctf" @click="recharge">
            <img class="user_zhonglan_zhonszctfimg" src="../../static/ailkimg/chonzhi.png" alt="">
            <view class="user_zhonglan_zhonszctf1">{{ $t('_Ailk.recharge') }}</view>
          </view>
          <view class="user_zhonglan_zhonszctfhr"></view>
          <view class="user_zhonglan_zhonszctf" @click="withdrawal">
            <img class="user_zhonglan_zhonszctfimg" src="../../static/ailkimg/tixian.png" alt="">
            <view class="user_zhonglan_zhonszctf1">{{ $t('_Ailk.withdrawal') }}</view>
          </view>
        </view>

      </view>
      <view class="user_tjgl">
        <view class="user_tjgl_box">
          <view class="user_tjgl_dj">{{ $t('_Ailk.referral_management') }}</view>
        </view>
        <view class="user_tjgl_boxlddq">
          <view class="user_tjgl_bq">{{ $t('_Ailk.referrer_count') }}</view>
          <view class="user_tjgl_ybq">
            <view class="user_tjgl_ybq1">{{ userasset.ref_user_total }}{{ $t('_Ailk.ren') }}</view>
            <view class="user_tjgl_ybq3" style="display: none;">{{ $t('_Ailk.bet_task_completed') }}</view>
          </view>
        </view>
        <view class="user_tjgl_boxlddq" @click="zwkf">
          <view class="user_tjgl_bq">{{ $t('_Ailk.sub_user_list') }}</view>
          <view class="user_tjgl_ybq">
            <img class="user_tjgl_ybqimg" src="../../static/ailkimg/meyoujintou.png" alt="">
          </view>
        </view>
        <view class="user_tjgl_boxlddq">
          <view class="user_tjgl_bq">{{ $t('_Ailk.invitation_link') }}</view>
          <view class="user_tjgl_bq">{{ userasset.invite_code }}</view>
        </view>
        <!-- <view class="user_tjgl_boxldbk">
          <view class="user_tjgl_boxldbk1">LP{{ $t('_Ailk.lp_injection') }}(</view>
          <view class="user_tjgl_boxldbk2">0.1BNB+{{ $t('_Ailk.equivalent') }}Ailk</view>
          <view class="user_tjgl_boxldbk1">)</view>
        </view> -->
        <view class="user_tjgl_button" @click="getyqlj">
          <view class="user_tjgl_buttonqd">{{ $t('_Ailk.copy_invitation') }}</view>
        </view>
      </view>
      <view class="user_tjgl">
        <view class="user_tjgl_box">
          <view class="user_tjgl_dj">{{ $t('_Ailk.merchant_center') }}</view>
        </view>
        <view class="user_tjgl_boxlddq">
          <view class="user_tjgl_bq">{{ $t('_Ailk.current_status') }}</view>
          <view class="user_tjgl_ybq">
            <view class="user_tjgl_ybq4" v-if="userasset.vip_level === 0">{{ $t('_Ailk.notactive') }}</view>
            <view class="user_tjgl_ybq4" v-if="userasset.vip_level === 1">{{ $t('_Ailk.activated') }}</view>
          </view>
        </view>
        <view class="user_tjgl_boxlddq">
          <view class="user_tjgl_bq">{{ $t('_Ailk.merchant_qualification') }}</view>
        </view>

        <!-- <view class="user_tjgl_boxldbklddq">
          <view class="user_tjgl_boxldbklddqxh">
            <view class="user_tjgl_boxldbklddqla">
              <view class="user_tjgl_boxldbklddqla1">LP{{ $t('_Ailk.lp_injection') }}(</view>
              <view class="user_tjgl_boxldbklddqla2">0.1BNB+{{ $t('_Ailk.equivalent') }}Ailk</view>
              <view class="user_tjgl_boxldbklddqla1">)</view>
            </view>
            <img class="user_tjgl_boxldbklddqimg" src="../../static/ailkimg/useryuangou.png" alt="">
          </view>
          <view class="user_tjgl_boxldbklddqxh">
            <view class="user_tjgl_boxldbklddqla">
              <view class="user_tjgl_boxldbklddqla1">Ailk{{ $t('_Ailk.prize_pool_participation') }}</view>
            </view>
            <img class="user_tjgl_boxldbklddqimg" src="../../static/ailkimg/useryuangou.png" alt="">
          </view>
        </view> -->

        <view class="user_tjgl_boxlddq">
          <view class="user_tjgl_bq">{{ $t('_Ailk.merchant_earnings') }}</view>
          <view class="user_tjgl_ybq">
            <view class="user_tjgl_ybq1">0.00 Ailk</view>
          </view>
        </view>

        <view class="user_tjgl_boxlddq" @click="zwkf">
          <view class="user_tjgl_bq">{{ $t('_Ailk.sub_merchant_list') }}</view>
          <view class="user_tjgl_ybq">
            <img class="user_tjgl_ybqimg" src="../../static/ailkimg/meyoujintou.png" alt="">
          </view>
        </view>

        <view class="user_tjgl_button" @click="djjhzh" style="display: none;">
          <view class="user_tjgl_buttonqd">{{ $t('_Ailk.activate_merchant') }}</view>
        </view>
      </view>
      <!-- <view class="user_tjgl">
        <view class="user_tjgl_box">
          <view class="user_tjgl_dj">{{ $t('_Ailk.reward_records') }}</view>
        </view>

        <view class="user_tjgl_boxlddq" @click="lingqujulu">
          <view class="user_tjgl_bq">{{ $t('_Ailk.lottery_reward') }}</view>
          <view class="user_tjgl_ybq">
            <img class="user_tjgl_ybqimg" src="../../static/ailkimg/meyoujintou.png" alt="">
          </view>
        </view>
        <view class="user_tjgl_boxlddq" @click="lingqujulu">
          <view class="user_tjgl_bq">{{ $t('_Ailk.cross_pool_dividend') }}</view>
          <view class="user_tjgl_ybq">
            <img class="user_tjgl_ybqimg" src="../../static/ailkimg/meyoujintou.png" alt="">
          </view>
        </view>
        <view class="user_tjgl_boxlddq" @click="lingqujulu">
          <view class="user_tjgl_bq">{{ $t('_Ailk.bet_pool_dividend') }}</view>
          <view class="user_tjgl_ybq">
            <img class="user_tjgl_ybqimg" src="../../static/ailkimg/meyoujintou.png" alt="">
          </view>
        </view>
        <view class="user_tjgl_boxlddq" @click="lingqujulu">
          <view class="user_tjgl_bq">{{ $t('_Ailk.order_reward') }}</view>
          <view class="user_tjgl_ybq">
            <img class="user_tjgl_ybqimg" src="../../static/ailkimg/meyoujintou.png" alt="">
          </view>
        </view>
      </view> -->
      <view class="baodansomin">
        <view class="baodansomin_tefr">{{ $t('_Ailk.merchant_rights') }}</view>
        <view class="baodansomin_cdcd">
          <view class="baodansomin_cdcdt">{{ $t('_Ailk.WeightedaveragedailyoutputofAilkbasedontheshareofLPmining') }}</view>
          <view class="baodansomin_cdcdt">{{ $t('_Ailk.Subdepartmentminingrewards') }}</view>
          <view class="baodansomin_cdcdt">{{ $t('_Ailk.Storecustomerlotterywinnings1reward') }}</view>
          <view class="baodansomin_cdcdt">{{ $t('_Ailk.Submerchantcustomerwinnings1reward') }}</view>
        </view>
      </view>
    </view>

  </view>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import Header from '../../components/common/header.vue';
import { getMemberInfo } from '../../api/ailk.js';
const userasset = ref({}); // 用户资产

// 跳转函数
const transferaccounts = () => {
  uni.navigateTo({
    url: '/pages/transferaccounts/transferaccounts'
  });
};
const receivepayment = () => {
  uni.navigateTo({
    url: '/pages/receivepayment/receivepayment'
  });
};
const withdrawal = () => {
  uni.navigateTo({
    url: '/pages/withdrawal/withdrawal'
  });
};
const recharge = () => {
  uni.navigateTo({
    url: '/pages/recharge/recharge'
  });
};
const lingqujulu = () => {
  uni.navigateTo({
    url: '/pages/collectionrecord/collectionrecord'
  });
};

// 组件挂载后执行
onMounted(() => {
  getquserInfos();
});
// 获取用户资产
const getquserInfos = async () => {
  const params = {

  };
  let res = await getMemberInfo(params)
  // console.log("获取用户资产:", res);

  if (res?.code === 200) {
    userasset.value = res?.data;
    console.log("获取用户资产:", userasset.value);
  } else {
    console.log({ message: res?.msg || "数据获取失败", type: "fail" });
  }
};

// 暂未开放
const zwkf = () => {
  uni.showToast({
    title: '暂未开放',
    icon: 'none',
    duration: 1000,
  });
};
// 复制邀请链接
const getyqlj = () => {
  if (userasset.value?.level === 0) {
    uni.showToast({
      title: '复制失败',
      icon: 'error',
      duration: 1000,
    });
    return;
  }
  uni.setClipboardData({
    data: `https://ailkapi.tocoinweb3.com/h5/index.html#/?invite_code=${userasset.value?.invite_code}`,
    success: function (res) {
      uni.showToast({
        title: '复制成功',
        icon: 'success',
        duration: 1000,
      });
    }
  });
};
// 点击激活
const djjhzh = () => {
  uni.showToast({
    title: '激活成功',
    icon: 'success',
    duration: 1000,
  });
};

</script>
<style lang="scss" scoped>
.me_profile {
  height: 100%;
  width: 100%;

  .user_profile {
    color: #ffffff;
    padding: 32rpx;
    height: 100%;
    width: 100%;
    box-sizing: border-box;

    .user_zhonglan {
      // width: 686rpx;
      height: 452rpx;
      padding: 0 40rpx;
      background: #02142A;
      border-radius: 24rpx 24rpx 24rpx 24rpx;
      border: 2rpx solid #004676;

      .user_zhonglan_top {
        // width: 192rpx;
        margin: 40rpx 0 24rpx;
        height: 44rpx;
        font-family: PingFang SC, PingFang SC;
        font-weight: 400;
        font-size: 32rpx;
        color: #FFFFFF;
        line-height: 38rpx;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }

      .user_zhonglan_zhon {
        // width: 606rpx;
        margin: 0 0 8rpx;
        height: 60rpx;
        background: #002A5D;
        border-radius: 8rpx;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .user_zhonglan_zhon_left_top {
          margin: 0 0 0 16rpx;
          height: 40rpx;
          font-family: PingFang SC, PingFang SC;
          font-weight: 400;
          font-size: 28rpx;
          color: #FFFFFF;
          line-height: 33rpx;
          text-align: left;
          font-style: normal;
          text-transform: none;
        }

        .user_zhonglan_zhon_left_bottom {
          margin: 0 16rpx 0 0;
          height: 40rpx;
          font-family: PingFang SC, PingFang SC;
          font-weight: 400;
          font-size: 28rpx;
          color: #FFFFFF;
          line-height: 33rpx;
          text-align: right;
          font-style: normal;
          text-transform: none;
        }
      }

      .user_zhonglan_zhonszct {
        // width: 606rpx;
        margin: 14rpx 0 0;
        height: 86rpx;
        background: #002A5D;
        border-radius: 8rpx;
        display: flex;
        align-items: center;
        justify-content: space-between;
        overflow-x: auto;
        padding: 0 20rpx;

        .user_zhonglan_zhonszctf {
          // width: 128.5rpx;
          height: 34.5rpx;
          display: flex;
          align-items: center;
          justify-content: space-evenly;

          .user_zhonglan_zhonszctfimg {
            width: 28rpx;
            height: 28rpx;
            margin: 6rpx 6rpx 0 0;
          }

          .user_zhonglan_zhonszctf1 {
            // width: 60rpx;
            // height: 30rpx;
            font-family: PingFang SC, PingFang SC;
            font-weight: 600;
            font-size: 30rpx;
            color: #FFFFFF;
            line-height: 30rpx;
            text-align: center;
            font-style: normal;
            text-transform: none;
            margin: 0 6rpx;
          }
        }

        .user_zhonglan_zhonszctfhr {
          width: 0rpx;
          height: 35rpx;
          border-radius: 0rpx 0rpx 0rpx 0rpx;
          border: 2rpx solid rgba(255, 255, 255, 0.22);
          margin: 0 6rpx;
        }
      }

    }

    .user_tjgl {
      // width: 686rpx;
      margin: 40rpx 0;
      // height: 586rpx;
      background: #02142A;
      border-radius: 24rpx 24rpx 24rpx 24rpx;
      border: 2rpx solid #004676;
      padding: 0 0 40rpx;

      .user_tjgl_box {
        height: 44rpx;
        margin: 40rpx 0 0 40rpx;
        display: flex;
        align-items: center;

        .user_tjgl_img {
          width: 32rpx;
          height: 32rpx;
          margin: 0 0 4rpx 0;
        }

        .user_tjgl_dj {
          // width: 136rpx;
          // margin: 0 0 0 24rpx;
          height: 44rpx;
          font-family: PingFang SC, PingFang SC;
          font-weight: 400;
          font-size: 32rpx;
          color: #FFFFFF;
          line-height: 38rpx;
          text-align: left;
          font-style: normal;
          text-transform: none;
        }
      }

      .user_tjgl_boxlddq {
        height: 44rpx;
        margin: 24rpx 40rpx 0;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .user_tjgl_bq {
          // width: 160rpx;
          // width: 40%;
          height: 44rpx;
          font-family: PingFang SC, PingFang SC;
          font-weight: 400;
          font-size: 32rpx;
          color: #FFFFFF;
          line-height: 38rpx;
          text-align: left;
          font-style: normal;
          text-transform: none;
        }

        .user_tjgl_ybq {
          height: 44rpx;
          display: flex;
          align-items: center;

          .user_tjgl_ybq1 {
            // width: 104rpx;
            height: 44rpx;
            font-family: PingFang SC, PingFang SC;
            font-weight: 400;
            font-size: 32rpx;
            color: #FFFFFF;
            line-height: 38rpx;
            text-align: right;
            font-style: normal;
            text-transform: none;
          }

          .user_tjgl_ybq2 {
            // width: 204rpx;
            margin: 0 0 0 8rpx;
            height: 40rpx;
            font-family: PingFang SC, PingFang SC;
            font-weight: 400;
            font-size: 28rpx;
            color: #71A5CE;
            line-height: 33rpx;
            text-align: right;
            font-style: normal;
            text-transform: none;
          }

          .user_tjgl_ybq3 {
            margin: 0 0 0 8rpx;
            width: 96rpx;
            height: 48rpx;
            font-family: PingFang SC, PingFang SC;
            font-weight: 400;
            font-size: 24rpx;
            color: #71A5CE;
            line-height: 24rpx;
            text-align: center;
            font-style: normal;
            text-transform: none;
          }

          .user_tjgl_ybq4 {
            // width: 96rpx;
            height: 44rpx;
            font-family: PingFang SC, PingFang SC;
            font-weight: 400;
            font-size: 32rpx;
            color: #39EAFD;
            line-height: 38rpx;
            text-align: right;
            font-style: normal;
            text-transform: none;
          }

          .user_tjgl_ybqfk {
            width: 40rpx;
            height: 40rpx;
            border-radius: 8rpx;
            border: 2rpx solid #39EAFD;
            margin: 0 0 0 8rpx;
          }

          .user_tjgl_ybqimg {
            width: 48rpx;
            height: 48rpx;
          }
        }
      }

      .user_tjgl_boxldbk {
        // width: 638rpx;
        margin: 24rpx;
        padding: 0 26rpx;
        height: 92rpx;
        background: #02142A;
        border-radius: 24rpx 24rpx 24rpx 24rpx;
        border: 2rpx solid #004676;
        display: flex;
        align-items: center;

        .user_tjgl_boxldbk1 {
          // width: 546rpx;
          height: 44rpx;
          font-family: PingFang SC, PingFang SC;
          font-weight: 400;
          font-size: 32rpx;
          color: #71A5CE;
          line-height: 38rpx;
          text-align: left;
          font-style: normal;
          text-transform: none;
        }

        .user_tjgl_boxldbk2 {
          // width: 546rpx;
          height: 44rpx;
          font-family: PingFang SC, PingFang SC;
          font-weight: 400;
          font-size: 32rpx;
          color: #39EAFD;
          line-height: 38rpx;
          text-align: left;
          font-style: normal;
          text-transform: none;
        }
      }

      .user_tjgl_boxldbklddq {
        margin: 24rpx;
        padding: 0 26rpx;
        height: 172rpx;
        background: #02142A;
        border-radius: 24rpx 24rpx 24rpx 24rpx;
        border: 2rpx solid #004676;
        display: flex;
        flex-direction: column;

        .user_tjgl_boxldbklddqxh {
          margin: 28rpx 0 0;
          height: 44rpx;
          display: flex;
          align-items: center;
          justify-content: space-between;

          .user_tjgl_boxldbklddqla {
            height: 44rpx;
            display: flex;
            align-items: center;

            .user_tjgl_boxldbklddqla1 {
              height: 44rpx;
              font-family: PingFang SC, PingFang SC;
              font-weight: 400;
              font-size: 32rpx;
              color: #71A5CE;
              line-height: 38rpx;
              text-align: left;
              font-style: normal;
              text-transform: none;
            }

            .user_tjgl_boxldbklddqla2 {
              height: 44rpx;
              font-family: PingFang SC, PingFang SC;
              font-weight: 400;
              font-size: 32rpx;
              color: #39EAFD;
              line-height: 38rpx;
              text-align: left;
              font-style: normal;
              text-transform: none;
            }
          }

          .user_tjgl_boxldbklddqimg {
            width: 44rpx;
            height: 44rpx;
          }

        }



      }

      .user_tjgl_button {
        // width: 640rpx;
        margin: 24rpx 23rpx 0;
        height: 70rpx;
        background: #3E89FF;
        border-radius: 8rpx;
        display: flex;
        align-items: center;
        justify-content: center;

        .user_tjgl_buttonqd {
          // width: 120rpx;
          height: 30rpx;
          font-family: PingFang SC, PingFang SC;
          font-weight: 600;
          font-size: 30rpx;
          color: #FFFFFF;
          line-height: 30rpx;
          text-align: center;
          font-style: normal;
          text-transform: none;
        }
      }
    }

    .baodansomin {
      // width: 686rpx;
      // height: 242rpx;
      background: #02142A;
      border-radius: 24rpx;
      border: 2rpx solid #004676;
      padding: 40rpx 40rpx 40rpx 0;

      .baodansomin_tefr {
        margin: 0 0 24rpx 40rpx;
        height: 44rpx;
        font-family: PingFang SC, PingFang SC;
        font-weight: 400;
        font-size: 32rpx;
        color: #FFFFFF;
        line-height: 38rpx;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }

      .baodansomin_cdcd {
        // height: 34rpx;
        margin: 0 0 0 40rpx;
        display: flex;
        flex-direction: column;
        align-items: flex-start;

        .baodansomin_cdcdy {
          width: 10rpx;
          height: 10rpx;
          background: #71A5CE;
          border-radius: 50%;
          margin: 0 10rpx 0 0;
        }

        .baodansomin_cdcdt {
          // width: 370rpx;
          // height: 34rpx;
          font-family: PingFang SC, PingFang SC;
          font-weight: 400;
          font-size: 24rpx;
          color: #71A5CE;
          // line-height: 28rpx;
          // text-align: left;
          // font-style: normal;
          // text-transform: none;
        }
      }
    }
  }
}
</style>