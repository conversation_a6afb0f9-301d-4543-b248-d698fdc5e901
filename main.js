import App from './App'
import messages from './locales/index.js'
import config from './config/index.js'
import { registerVant } from './utils/registerVant'

// #ifdef H5
import { Buffer } from 'buffer'
window.Buffer = Buffer
// #endif

// 语言配置跨平台处理
let lang = 'cn'
// #ifdef H5
lang = sessionStorage.getItem('lang') || localStorage.getItem("lang")  || 'cn'
// #endif

const i18nConfig = {
  locale: lang,
  messages,
  silentTranslationWarn: true,
  missingWarn: false,
  silentFallbackWarn: true,
  fallbackWarn: false,
}

// 先在外层声明 i18n，不要再用 const/let 重复声明
let i18n

// #ifndef VUE3
import Vue from 'vue'
import VueI18n from 'vue-i18n'
import './uni.promisify.adaptor'

Vue.config.productionTip = false
Vue.use(VueI18n)

// 在 Vue2 分支里给外层的 i18n 变量赋值
i18n = new VueI18n(i18nConfig)
App.mpType = 'app'

// 注册 Vant（Vue2 这里传入 Vue 构造函数）
registerVant(Vue)

const app = new Vue({
  i18n,
  ...App
})
app.$mount()
// #endif

// #ifdef VUE3
import { createSSRApp } from 'vue'
import { createI18n } from 'vue-i18n'

// 在 Vue3 分支里给同一个 i18n 变量赋值
i18n = createI18n(i18nConfig)

export function createApp() {
  const app = createSSRApp(App)
  registerVant(app)
  app.use(i18n)
  return {
    app
  }
}
// #endif
